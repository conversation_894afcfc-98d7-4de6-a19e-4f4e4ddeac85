{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MsIGJhdGNoSW1wb3J0UXVlc3Rpb25zLCBleHBvcnRRdWVzdGlvbnNUb1dvcmQgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlF1ZXN0aW9uQmFua0RldGFpbCIsCiAgY29tcG9uZW50czogewogICAgUXVlc3Rpb25DYXJkLAogICAgUXVlc3Rpb25Gb3JtCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6aKY5bqT5L+h5oGvCiAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgYmFua05hbWU6ICcnLAogICAgICAvLyDnu5/orqHmlbDmja4KICAgICAgc3RhdGlzdGljczogewogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHNpbmdsZUNob2ljZTogMCwKICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICBqdWRnbWVudDogMAogICAgICB9LAogICAgICAvLyDpopjnm67liJfooagKICAgICAgcXVlc3Rpb25MaXN0OiBbXSwKICAgICAgLy8g5YiG6aG15Y+C5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgICBxdWVzdGlvblR5cGU6IG51bGwsCiAgICAgICAgZGlmZmljdWx0eTogbnVsbCwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IG51bGwKICAgICAgfSwKICAgICAgLy8g5bGV5byA54q25oCBCiAgICAgIGV4cGFuZEFsbDogZmFsc2UsCiAgICAgIGV4cGFuZGVkUXVlc3Rpb25zOiBbXSwKICAgICAgLy8g6YCJ5oup54q25oCBCiAgICAgIHNlbGVjdGVkUXVlc3Rpb25zOiBbXSwKICAgICAgaXNBbGxTZWxlY3RlZDogZmFsc2UsCiAgICAgIC8vIOihqOWNleebuOWFswogICAgICBxdWVzdGlvbkZvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudFF1ZXN0aW9uVHlwZTogJ3NpbmdsZScsCiAgICAgIGN1cnJlbnRRdWVzdGlvbkRhdGE6IG51bGwsCiAgICAgIC8vIOaJuemHj+WvvOWFpQogICAgICBpbXBvcnREcmF3ZXJWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5paH5qGj5a+85YWl5oq95bGJCiAgICAgIGRvY3VtZW50Q29udGVudDogJycsCiAgICAgIGRvY3VtZW50SHRtbENvbnRlbnQ6ICcnLAogICAgICBwYXJzZWRRdWVzdGlvbnM6IFtdLAogICAgICBwYXJzZUVycm9yczogW10sCiAgICAgIGFsbEV4cGFuZGVkOiB0cnVlLAogICAgICBpc1NldHRpbmdGcm9tQmFja2VuZDogZmFsc2UsCiAgICAgIGRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHJ1bGVzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGFjdGl2ZVJ1bGVUYWI6ICdleGFtcGxlcycsCiAgICAgIC8vIOS4iuS8oOWSjOino+aekOeKtuaAgQogICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgIGlzUGFyc2luZzogZmFsc2UsCiAgICAgIGltcG9ydGluZ1F1ZXN0aW9uczogZmFsc2UsCiAgICAgIGltcG9ydFByb2dyZXNzOiAwLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIC8vIOaWh+S7tuS4iuS8oAogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2Jpei9xdWVzdGlvbkJhbmsvdXBsb2FkRG9jdW1lbnQnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgICB9LAogICAgICB1cGxvYWREYXRhOiB7fSwKICAgICAgLy8g5a+M5paH5pys57yW6L6R5ZmoCiAgICAgIHJpY2hFZGl0b3I6IG51bGwsCiAgICAgIGVkaXRvckluaXRpYWxpemVkOiBmYWxzZQogICAgfQogIH0sCgogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzmlofmoaPlhoXlrrnlj5jljJbvvIzoh6rliqjop6PmnpAKICAgIGRvY3VtZW50Q29udGVudDogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIC8vIOWmguaenOaYr+S7juWQjuerr+iuvue9ruWGheWuue+8jOS4jeinpuWPkeWJjeerr+ino+aekAogICAgICAgIGlmICh0aGlzLmlzU2V0dGluZ0Zyb21CYWNrZW5kKSB7CiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLnRyaW0oKSkgewogICAgICAgICAgdGhpcy5kZWJvdW5jZVBhcnNlRG9jdW1lbnQoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogZmFsc2UKICAgIH0sCiAgICAvLyDnm5HlkKzmir3lsYnmiZPlvIDnirbmgIEKICAgIGltcG9ydERyYXdlclZpc2libGU6IHsKICAgICAgaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICBpZiAobmV3VmFsKSB7CiAgICAgICAgICAvLyDmir3lsYnmiZPlvIDml7bmuIXnqbrmiYDmnInlhoXlrrnlubbliJ3lp4vljJbnvJbovpHlmagKICAgICAgICAgIHRoaXMuY2xlYXJJbXBvcnRDb250ZW50KCkKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5pbml0UmljaEVkaXRvcigpCiAgICAgICAgICB9KQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmir3lsYnlhbPpl63ml7bplIDmr4HnvJbovpHlmagKICAgICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IpIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSBudWxsCiAgICAgICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiBmYWxzZQogICAgfQogIH0sCgogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXRQYWdlKCkKICAgIC8vIOWIm+W7uumYsuaKluWHveaVsCAtIOWinuWKoOW7tuaXtuWIsDLnp5LvvIzlh4/lsJHljaHpob8KICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50ID0gdGhpcy5kZWJvdW5jZSh0aGlzLnBhcnNlRG9jdW1lbnQsIDIwMDApCiAgICAvLyDliJvlu7rnvJbovpHlmajlhoXlrrnlj5jljJbnmoTpmLLmipblh73mlbAgLSDlu7bml7YxLjXnp5IKICAgIHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlID0gdGhpcy5kZWJvdW5jZSh0aGlzLmhhbmRsZUVkaXRvckNvbnRlbnRDaGFuZ2VEZWJvdW5jZWQsIDE1MDApCiAgICAvLyDliJ3lp4vljJbkuIrkvKDmlbDmja4KICAgIHRoaXMudXBsb2FkRGF0YSA9IHsKICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZAogICAgfQogICAgdGhpcy51cGxvYWRIZWFkZXJzID0gewogICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyB0aGlzLiRzdG9yZS5nZXR0ZXJzLnRva2VuCiAgICB9CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIC8vIOe8lui+keWZqOWwhuWcqOaKveWxieaJk+W8gOaXtuWIneWni+WMlgoKICB9LAoKICBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g5Y+W5raI5omA5pyJ6Ziy5oqW5Ye95pWwCiAgICBpZiAodGhpcy5kZWJvdW5jZVBhcnNlRG9jdW1lbnQgJiYgdGhpcy5kZWJvdW5jZVBhcnNlRG9jdW1lbnQuY2FuY2VsKSB7CiAgICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50LmNhbmNlbCgpCiAgICB9CiAgICBpZiAodGhpcy5kZWJvdW5jZUVkaXRvckNvbnRlbnRDaGFuZ2UgJiYgdGhpcy5kZWJvdW5jZUVkaXRvckNvbnRlbnRDaGFuZ2UuY2FuY2VsKSB7CiAgICAgIHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlLmNhbmNlbCgpCiAgICB9CgogICAgLy8g6ZSA5q+B5a+M5paH5pys57yW6L6R5ZmoCiAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCkKICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yid5aeL5YyW6aG16Z2iCiAgICBpbml0UGFnZSgpIHsKICAgICAgY29uc3QgeyBiYW5rSWQsIGJhbmtOYW1lIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQogICAgICBpZiAoIWJhbmtJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e8uuWwkemimOW6k0lE5Y+C5pWwJykKICAgICAgICB0aGlzLmdvQmFjaygpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5iYW5rSWQgPSBiYW5rSWQKICAgICAgdGhpcy5iYW5rTmFtZSA9IGJhbmtOYW1lIHx8ICfpopjlupPor6bmg4UnCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmFua0lkID0gYmFua0lkCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgIH0sCiAgICAvLyDov5Tlm57popjlupPliJfooagKICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLmJhY2soKQogICAgfSwKICAgIC8vIOiOt+WPlumimOebruWIl+ihqAogICAgZ2V0UXVlc3Rpb25MaXN0KCkgewogICAgICAvLyDovazmjaLmn6Xor6Llj4LmlbDmoLzlvI8KICAgICAgY29uc3QgcGFyYW1zID0gdGhpcy5jb252ZXJ0UXVlcnlQYXJhbXModGhpcy5xdWVyeVBhcmFtcykKICAgICAgbGlzdFF1ZXN0aW9uKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5xdWVzdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bpopjnm67liJfooajlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvLyDovazmjaLmn6Xor6Llj4LmlbDmoLzlvI8KICAgIGNvbnZlcnRRdWVyeVBhcmFtcyhwYXJhbXMpIHsKICAgICAgY29uc3QgY29udmVydGVkUGFyYW1zID0geyAuLi5wYXJhbXMgfQoKICAgICAgLy8g6L2s5o2i6aKY5Z6LCiAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlKSB7CiAgICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAgICdzaW5nbGUnOiAxLAogICAgICAgICAgJ211bHRpcGxlJzogMiwKICAgICAgICAgICdqdWRnbWVudCc6IDMKICAgICAgICB9CiAgICAgICAgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZSA9IHR5cGVNYXBbY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZV0gfHwgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZQogICAgICB9CgogICAgICAvLyDovazmjaLpmr7luqYKICAgICAgaWYgKGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5KSB7CiAgICAgICAgY29uc3QgZGlmZmljdWx0eU1hcCA9IHsKICAgICAgICAgICfnroDljZUnOiAxLAogICAgICAgICAgJ+S4reetiSc6IDIsCiAgICAgICAgICAn5Zuw6Zq+JzogMwogICAgICAgIH0KICAgICAgICBjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXBbY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHldIHx8IGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5CiAgICAgIH0KCiAgICAgIC8vIOa4heeQhuepuuWAvAogICAgICBPYmplY3Qua2V5cyhjb252ZXJ0ZWRQYXJhbXMpLmZvckVhY2goa2V5ID0+IHsKICAgICAgICBpZiAoY29udmVydGVkUGFyYW1zW2tleV0gPT09ICcnIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSBudWxsIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGRlbGV0ZSBjb252ZXJ0ZWRQYXJhbXNba2V5XQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBjb252ZXJ0ZWRQYXJhbXMKICAgIH0sCiAgICAvLyDojrflj5bnu5/orqHmlbDmja4KICAgIGdldFN0YXRpc3RpY3MoKSB7CiAgICAgIGdldFF1ZXN0aW9uU3RhdGlzdGljcyh0aGlzLmJhbmtJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gcmVzcG9uc2UuZGF0YQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uCiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gewogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICAgIGp1ZGdtZW50OiAwCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCgogICAgLy8g5aSE55CG5om56YeP5a+86aKY5oyJ6ZKu54K55Ye7CiAgICBoYW5kbGVCYXRjaEltcG9ydENsaWNrKCkgewogICAgICB0aGlzLmltcG9ydERyYXdlclZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5re75Yqg6aKY55uuCiAgICBoYW5kbGVBZGRRdWVzdGlvbih0eXBlKSB7CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHR5cGUKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gbnVsbAogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5YiH5o2i5bGV5byA54q25oCBCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7CiAgICAgIHRoaXMuZXhwYW5kQWxsID0gIXRoaXMuZXhwYW5kQWxsCiAgICAgIGlmICghdGhpcy5leHBhbmRBbGwpIHsKICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zID0gW10KICAgICAgfQogICAgfSwKCgoKICAgIC8vIOWvvOWHuumimOebrgogICAgaGFuZGxlRXhwb3J0UXVlc3Rpb25zKCkgewogICAgICAvLyDnoa7orqTlr7zlh7oKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5a+85Ye66aKY5bqTIiR7dGhpcy5iYW5rTmFtZX0i5Lit55qE5omA5pyJ6aKY55uu5ZCX77yfYCwgJ+WvvOWHuuehruiupCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumuWvvOWHuicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ2luZm8nCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICB0ZXh0OiBg5q2j5Zyo5a+85Ye66aKY5bqT5Lit55qE5omA5pyJ6aKY55uuLi4uYCwKICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLAogICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC43KScKICAgICAgICB9KQoKICAgICAgICAvLyDosIPnlKjlr7zlh7pBUEkgLSDlr7zlh7rlvZPliY3popjlupPnmoTmiYDmnInpopjnm64KICAgICAgICBleHBvcnRRdWVzdGlvbnNUb1dvcmQoewogICAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICAgIGJhbmtOYW1lOiB0aGlzLmJhbmtOYW1lCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCkKCiAgICAgICAgICAvLyDliJvlu7rkuIvovb3pk77mjqUKICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VdLCB7CiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcKICAgICAgICAgIH0pCiAgICAgICAgICBjb25zdCB1cmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKQogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQogICAgICAgICAgbGluay5ocmVmID0gdXJsCiAgICAgICAgICBsaW5rLmRvd25sb2FkID0gYCR7dGhpcy5iYW5rTmFtZX0uZG9jeGAKICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykKICAgICAgICAgIGxpbmsuY2xpY2soKQogICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKQoKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5a+85Ye66aKY5bqTIiR7dGhpcy5iYW5rTmFtZX0iYCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCkKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWHuuWksei0pTonLCBlcnJvcikKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWHuuWksei0pe+8jOivt+mHjeivlScpCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOeUqOaIt+WPlua2iOWvvOWHugogICAgICB9KQogICAgfSwKCiAgICAvLyDliIfmjaLlhajpgIkv5YWo5LiN6YCJCiAgICBoYW5kbGVUb2dnbGVTZWxlY3RBbGwoKSB7CiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9ICF0aGlzLmlzQWxsU2VsZWN0ZWQKICAgICAgaWYgKHRoaXMuaXNBbGxTZWxlY3RlZCkgewogICAgICAgIC8vIOWFqOmAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSB0aGlzLnF1ZXN0aW9uTGlzdC5tYXAocSA9PiBxLnF1ZXN0aW9uSWQpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LpgInmi6kgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhajkuI3pgIkKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWPlua2iOmAieaLqeaJgOaciemimOebricpCiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDmibnph4/liKDpmaTvvIjkvJjljJbniYjmnKzvvIkKICAgIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IGRlbGV0ZUNvdW50ID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgKICAgICAgbGV0IGNvbmZpcm1NZXNzYWdlID0gYOehruiupOWIoOmZpOmAieS4reeahCAke2RlbGV0ZUNvdW50fSDpgZPpopjnm67lkJfvvJ9gCgogICAgICBpZiAoZGVsZXRlQ291bnQgPiAyMCkgewogICAgICAgIGNvbmZpcm1NZXNzYWdlICs9ICdcblxu5rOo5oSP77ya6aKY55uu6L6D5aSa77yM5Yig6Zmk5Y+v6IO96ZyA6KaB5LiA5Lqb5pe26Ze077yM6K+36ICQ5b+D562J5b6F44CCJwogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKGNvbmZpcm1NZXNzYWdlLCAn5om56YeP5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5Yig6ZmkJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiBmYWxzZQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLnBlcmZvcm1CYXRjaERlbGV0ZSgpCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suWPlua2iOWIoOmZpCcpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOaJp+ihjOaJuemHj+WIoOmZpAogICAgYXN5bmMgcGVyZm9ybUJhdGNoRGVsZXRlKCkgewogICAgICBjb25zdCBkZWxldGVDb3VudCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoCiAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgIHRleHQ6IGDmraPlnKjliKDpmaQgJHtkZWxldGVDb3VudH0g6YGT6aKY55uu77yM6K+356iN5YCZLi4uYCwKICAgICAgICBzcGlubmVyOiAnZWwtaWNvbi1sb2FkaW5nJywKICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJwogICAgICB9KQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDkvb/nlKjnnJ/mraPnmoTmibnph4/liKDpmaRBUEkKICAgICAgICBjb25zdCBxdWVzdGlvbklkcyA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuam9pbignLCcpCiAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKQoKICAgICAgICBhd2FpdCBkZWxRdWVzdGlvbihxdWVzdGlvbklkcykgLy8g6LCD55So5om56YeP5Yig6ZmkQVBJCgogICAgICAgIGNvbnN0IGVuZFRpbWUgPSBEYXRlLm5vdygpCiAgICAgICAgY29uc3QgZHVyYXRpb24gPSAoKGVuZFRpbWUgLSBzdGFydFRpbWUpIC8gMTAwMCkudG9GaXhlZCgxKQoKICAgICAgICBsb2FkaW5nLmNsb3NlKCkKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WIoOmZpCAke2RlbGV0ZUNvdW50fSDpgZPpopjnm64gKOiAl+aXtiAke2R1cmF0aW9ufXMpYCkKCiAgICAgICAgLy8g5riF55CG6YCJ5oup54q25oCBCiAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gZmFsc2UKCiAgICAgICAgLy8g5Yi35paw5pWw5o2uCiAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGxvYWRpbmcuY2xvc2UoKQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+WIoOmZpOWksei0pTonLCBlcnJvcikKCiAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICfmibnph4/liKDpmaTlpLHotKUnCiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlICYmIGVycm9yLnJlc3BvbnNlLmRhdGEgJiYgZXJyb3IucmVzcG9uc2UuZGF0YS5tc2cpIHsKICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlLmRhdGEubXNnCiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7CiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlCiAgICAgICAgfQoKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yTWVzc2FnZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDpopjnm67pgInmi6nnirbmgIHlj5jljJYKICAgIGhhbmRsZVF1ZXN0aW9uU2VsZWN0KHF1ZXN0aW9uSWQsIHNlbGVjdGVkKSB7CiAgICAgIGlmIChzZWxlY3RlZCkgewogICAgICAgIGlmICghdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbklkKSkgewogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uSWQpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuc3BsaWNlKGluZGV4LCAxKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5pu05paw5YWo6YCJ54q25oCBCiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSB0aGlzLnF1ZXN0aW9uTGlzdC5sZW5ndGgKICAgIH0sCiAgICAvLyDliIfmjaLljZXkuKrpopjnm67lsZXlvIDnirbmgIEKICAgIGhhbmRsZVRvZ2dsZUV4cGFuZChxdWVzdGlvbklkKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgLy8g5pS26LW36aKY55uuCiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgLy8g5aaC5p6c5b2T5YmN5pivIuWxleW8gOaJgOaciSLnirbmgIHvvIzliJnlj5bmtogi5bGV5byA5omA5pyJIueKtuaAgQogICAgICAgIGlmICh0aGlzLmV4cGFuZEFsbCkgewogICAgICAgICAgdGhpcy5leHBhbmRBbGwgPSBmYWxzZQogICAgICAgICAgLy8g5bCG5YW25LuW6aKY55uu5re75Yqg5YiwZXhwYW5kZWRRdWVzdGlvbnPmlbDnu4TkuK3vvIzpmaTkuoblvZPliY3opoHmlLbotbfnmoTpopjnm64KICAgICAgICAgIHRoaXMucXVlc3Rpb25MaXN0LmZvckVhY2gocXVlc3Rpb24gPT4gewogICAgICAgICAgICBpZiAocXVlc3Rpb24ucXVlc3Rpb25JZCAhPT0gcXVlc3Rpb25JZCAmJiAhdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbi5xdWVzdGlvbklkKSkgewogICAgICAgICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbi5xdWVzdGlvbklkKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlsZXlvIDpopjnm64KICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLnB1c2gocXVlc3Rpb25JZCkKICAgICAgfQogICAgfSwKICAgIC8vIOe8lui+kemimOebrgogICAgaGFuZGxlRWRpdFF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uRGF0YSA9IHF1ZXN0aW9uCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZQogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5aSN5Yi26aKY55uuCiAgICBoYW5kbGVDb3B5UXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgLy8g5Yib5bu65aSN5Yi255qE6aKY55uu5pWw5o2u77yI56e76ZmkSUTnm7jlhbPlrZfmrrXvvIkKICAgICAgY29uc3QgY29waWVkUXVlc3Rpb24gPSB7CiAgICAgICAgLi4ucXVlc3Rpb24sCiAgICAgICAgcXVlc3Rpb25JZDogbnVsbCwgIC8vIOa4hemZpElE77yM6KGo56S65paw5aKeCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwKICAgICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIHVwZGF0ZUJ5OiBudWxsCiAgICAgIH0KCiAgICAgIC8vIOiuvue9ruS4uue8lui+keaooeW8j+W5tuaJk+W8gOihqOWNlQogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbkRhdGEgPSBjb3BpZWRRdWVzdGlvbgogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSB0aGlzLmNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyhxdWVzdGlvbi5xdWVzdGlvblR5cGUpCiAgICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IHRydWUKICAgIH0sCgogICAgLy8g6aKY5Z6L5pWw5a2X6L2s5a2X56ym5Liy77yI55So5LqO5aSN5Yi25Yqf6IO977yJCiAgICBjb252ZXJ0UXVlc3Rpb25UeXBlVG9TdHJpbmcodHlwZSkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgIDE6ICdzaW5nbGUnLAogICAgICAgIDI6ICdtdWx0aXBsZScsCiAgICAgICAgMzogJ2p1ZGdtZW50JwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8IHR5cGUKICAgIH0sCiAgICAvLyDliKDpmaTpopjnm64KICAgIGhhbmRsZURlbGV0ZVF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9uQ29udGVudCA9IHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudC5yZXBsYWNlKC88W14+XSo+L2csICcnKQogICAgICBjb25zdCBkaXNwbGF5Q29udGVudCA9IHF1ZXN0aW9uQ29udGVudC5sZW5ndGggPiA1MCA/IHF1ZXN0aW9uQ29udGVudC5zdWJzdHJpbmcoMCwgNTApICsgJy4uLicgOiBxdWVzdGlvbkNvbnRlbnQKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5Yig6Zmk6aKY55uuIiR7ZGlzcGxheUNvbnRlbnR9IuWQl++8n2AsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBkZWxRdWVzdGlvbihxdWVzdGlvbi5xdWVzdGlvbklkKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk6aKY55uu5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8vIOmimOebruihqOWNleaIkOWKn+WbnuiwgwogICAgaGFuZGxlUXVlc3Rpb25Gb3JtU3VjY2VzcygpIHsKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKCgoKICAgIC8vIOaKveWxieWFs+mXreWJjeWkhOeQhgogICAgaGFuZGxlRHJhd2VyQ2xvc2UoZG9uZSkgewogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnKrkv53lrZjnmoTlhoXlrrkKICAgICAgY29uc3QgaGFzQ29udGVudCA9IHRoaXMuZG9jdW1lbnRDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKS5sZW5ndGggPiAwCiAgICAgIGNvbnN0IGhhc1BhcnNlZFF1ZXN0aW9ucyA9IHRoaXMucGFyc2VkUXVlc3Rpb25zICYmIHRoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aCA+IDAKCiAgICAgIGlmIChoYXNDb250ZW50IHx8IGhhc1BhcnNlZFF1ZXN0aW9ucykgewogICAgICAgIGxldCBtZXNzYWdlID0gJ+WFs+mXreWQjuWwhuS4ouWkseW9k+WJjee8lui+keeahOWGheWuue+8jOehruiupOWFs+mXreWQl++8nycKICAgICAgICBpZiAoaGFzUGFyc2VkUXVlc3Rpb25zKSB7CiAgICAgICAgICBtZXNzYWdlID0gYOW9k+WJjeW3suino+aekOWHuiAke3RoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu77yM5YWz6Zet5ZCO5bCG5Lii5aSx5omA5pyJ5YaF5a6577yM56Gu6K6k5YWz6Zet5ZCX77yfYAogICAgICAgIH0KCiAgICAgICAgdGhpcy4kY29uZmlybShtZXNzYWdlLCAn56Gu6K6k5YWz6ZetJywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrprlhbPpl60nLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+e7p+e7ree8lui+kScsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIC8vIOa4heepuuWGheWuuQogICAgICAgICAgdGhpcy5jbGVhckltcG9ydENvbnRlbnQoKQogICAgICAgICAgZG9uZSgpCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgLy8g5Y+W5raI5YWz6Zet77yM57un57ut57yW6L6RCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDmsqHmnInlhoXlrrnnm7TmjqXlhbPpl60KICAgICAgICBkb25lKCkKICAgICAgfQogICAgfSwKCiAgICAvLyDmuIXnqbrlr7zlhaXlhoXlrrkKICAgIGNsZWFySW1wb3J0Q29udGVudCgpIHsKICAgICAgLy8g5riF56m65paH5qGj5YaF5a65CiAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gJycKICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJycKCiAgICAgIC8vIOa4heepuuino+aekOe7k+aenAogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXQoKICAgICAgLy8g6YeN572u6Kej5p6Q54q25oCBCiAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSB0cnVlCiAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSBmYWxzZQoKICAgICAgLy8g6YeN572u5LiK5Lyg54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICAgIHRoaXMuaW1wb3J0aW5nUXVlc3Rpb25zID0gZmFsc2UKICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDAKCiAgICAgIC8vIOmHjee9ruWvvOWFpemAiemhuQogICAgICB0aGlzLmltcG9ydE9wdGlvbnMgPSB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0KICAgIH0sCgogICAgLy8g5pi+56S65paH5qGj5a+85YWl5a+56K+d5qGGCiAgICBzaG93RG9jdW1lbnRJbXBvcnREaWFsb2coKSB7CiAgICAgIC8vIOa4hemZpOS4iuS4gOasoeeahOS4iuS8oOeKtuaAgeWSjOWGheWuuQogICAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQoKICAgICAgLy8g5riF6Zmk5LiK5Lyg57uE5Lu255qE5paH5Lu25YiX6KGoCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBjb25zdCB1cGxvYWRDb21wb25lbnQgPSB0aGlzLiRyZWZzLmRvY3VtZW50VXBsb2FkCiAgICAgICAgaWYgKHVwbG9hZENvbXBvbmVudCkgewogICAgICAgICAgdXBsb2FkQ29tcG9uZW50LmNsZWFyRmlsZXMoKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHRoaXMuZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlID0gdHJ1ZQoKICAgIH0sCgogICAgLy8g5pi+56S66KeE6IyD5a+56K+d5qGGCiAgICBzaG93UnVsZXNEaWFsb2coKSB7CiAgICAgIHRoaXMuYWN0aXZlUnVsZVRhYiA9ICdleGFtcGxlcycgLy8g6buY6K6k5pi+56S66IyD5L6L5qCH562+6aG1CiAgICAgIHRoaXMucnVsZXNEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDlsIbojIPkvovlpI3liLbliLDnvJbovpHljLogLSDlj6rkv53nlZnliY0z6aKY77ya5Y2V6YCJ44CB5aSa6YCJ44CB5Yik5patCiAgICBjb3B5RXhhbXBsZVRvRWRpdG9yKCkgewogICAgICAvLyDkvb/nlKjovpPlhaXojIPkvovmoIfnrb7pobXph4znmoTliY0z6aKY5YaF5a6577yM6L2s5o2i5Li6SFRNTOagvOW8jwogICAgICBjb25zdCBodG1sVGVtcGxhdGUgPSBgCjxwPjEu77yIICDvvInmmK/miJHlm73mnIDml6nnmoTor5fmrYzmgLvpm4bvvIzlj4jnp7DkvZwi6K+X5LiJ55m+IuOAgjwvcD4KPHA+QS7jgIrlt6bkvKDjgIs8L3A+CjxwPkIu44CK56a76aqa44CLPC9wPgo8cD5DLuOAiuWdm+e7j+OAizwvcD4KPHA+RC7jgIror5fnu4/jgIs8L3A+CjxwPuetlOahiO+8mkQ8L3A+CjxwPuino+aekO+8muivl+e7j+aYr+aIkeWbveacgOaXqeeahOivl+atjOaAu+mbhuOAgjwvcD4KPHA+6Zq+5bqm77ya5Lit562JPC9wPgo8cD48YnI+PC9wPgoKPHA+Mi7kuK3ljY7kurrmsJHlhbHlkozlm73nmoTmiJDnq4vvvIzmoIflv5fnnYDvvIgg77yJ44CCPC9wPgo8cD5BLuS4reWbveaWsOawkeS4u+S4u+S5iemdqeWRveWPluW+l+S6huWfuuacrOiDnOWIqTwvcD4KPHA+Qi7kuK3lm73njrDku6Plj7LnmoTlvIDlp4s8L3A+CjxwPkMu5Y2K5q6W5rCR5Zyw5Y2K5bCB5bu656S+5Lya55qE57uT5p2fPC9wPgo8cD5ELuS4reWbvei/m+WFpeekvuS8muS4u+S5ieekvuS8mjwvcD4KPHA+562U5qGI77yaQUJDPC9wPgo8cD7op6PmnpDvvJrmlrDkuK3lm73nmoTmiJDnq4vvvIzmoIflv5fnnYDmiJHlm73mlrDmsJHkuLvkuLvkuYnpnanlkb3pmLbmrrXnmoTln7rmnKznu5PmnZ/lkoznpL7kvJrkuLvkuYnpnanlkb3pmLbmrrXnmoTlvIDlp4vjgII8L3A+CjxwPjxicj48L3A+Cgo8cD4zLuWFg+adguWJp+eahOWbm+Wkp+aCsuWJp+aYr++8muWFs+axieWNv+eahOOAiueqpuWopeWGpOOAi++8jOmprOiHtOi/nOeahOOAiuaxieWuq+eni+OAi++8jOeZveactOeahOOAiuaip+ahkOmbqOOAi+WSjOmDkeWFieellueahOOAiui1teawj+WtpOWEv+OAi+OAgjwvcD4KPHA+562U5qGI77ya6ZSZ6K+vPC9wPgo8cD7op6PmnpDvvJrlhYPmnYLliafjgIrotbXmsI/lraTlhL/jgIvlhajlkI3jgIrlhqTmiqXlhqTotbXmsI/lraTlhL/jgIvvvIzkuLrnuqrlkJvnpaXmiYDkvZzjgII8L3A+CiAgICAgIGAudHJpbSgpCgogICAgICAvLyDnm7TmjqXorr7nva7liLDlr4zmlofmnKznvJbovpHlmagKICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpznvJbovpHlmajmnKrliJ3lp4vljJbvvIznrYnlvoXliJ3lp4vljJblkI7lho3orr7nva4KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CgogICAgICAvLyDlhbPpl63lr7nor53moYYKICAgICAgdGhpcy5ydWxlc0RpYWxvZ1Zpc2libGUgPSBmYWxzZQoKICAgICAgLy8g5o+Q56S655So5oi3CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6L6T5YWl6IyD5L6L5bey5aGr5YWF5Yiw57yW6L6R5Yy677yM5Y+z5L6n5bCG6Ieq5Yqo6Kej5p6QJykKCgogICAgfSwKCgoKICAgIC8vIOS4i+i9vVdvcmTmqKHmnb8KICAgIGRvd25sb2FkV29yZFRlbXBsYXRlKCkgewogICAgICB0aGlzLmRvd25sb2FkKCdiaXovcXVlc3Rpb25CYW5rL2Rvd25sb2FkV29yZFRlbXBsYXRlJywge30sIGDpopjnm67lr7zlhaVXb3Jk5qih5p2/LmRvY3hgKQogICAgfSwKCiAgICAvLyDkuIrkvKDliY3mo4Dmn6UKICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7CgoKICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgfHwKICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS5uYW1lLmVuZHNXaXRoKCcuZG9jeCcpIHx8IGZpbGUubmFtZS5lbmRzV2l0aCgnLnhsc3gnKQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMAoKICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oCAuZG9jeCDmiJYgLnhsc3gg5qC85byP55qE5paH5Lu2IScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgLy8g5pu05paw5LiK5Lyg5pWw5o2uCiAgICAgIHRoaXMudXBsb2FkRGF0YS5iYW5rSWQgPSB0aGlzLmJhbmtJZAoKICAgICAgLy8g6K6+572u5LiK5Lyg54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKCgoKICAgICAgcmV0dXJuIHRydWUKICAgIH0sCgogICAgLy8g5LiK5Lyg5oiQ5YqfCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlc3BvbnNlKSB7CiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAvLyDkuIrkvKDlrozmiJDvvIzlvIDlp4vop6PmnpAKICAgICAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmlzUGFyc2luZyA9IHRydWUKCgoKICAgICAgICAvLyDmuIXpmaTkuYvliY3nmoTop6PmnpDnu5PmnpzvvIznoa7kv53lubLlh4DnmoTlvIDlp4sKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCgogICAgICAgIC8vIOW7tui/n+WFs+mXreWvueivneahhu+8jOiuqeeUqOaIt+eci+WIsOino+aekOWKqOeUuwogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgdGhpcy5kb2N1bWVudEltcG9ydERpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQogICAgICAgIH0sIDE1MDApCgogICAgICAgIC8vIOiuvue9ruagh+W/l+S9je+8jOmBv+WFjeinpuWPkeWJjeerr+mHjeaWsOino+aekAogICAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSB0cnVlCgogICAgICAgIC8vIOWwhuino+aekOe7k+aenOaYvuekuuWcqOWPs+S+pwogICAgICAgIGlmIChyZXNwb25zZS5xdWVzdGlvbnMgJiYgcmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gcmVzcG9uc2UucXVlc3Rpb25zLm1hcChxdWVzdGlvbiA9PiAoewogICAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgICAgY29sbGFwc2VkOiBmYWxzZSAgLy8g6buY6K6k5bGV5byACiAgICAgICAgICB9KSkKICAgICAgICAgIC8vIOmHjee9ruWFqOmDqOWxleW8gOeKtuaAgQogICAgICAgICAgdGhpcy5hbGxFeHBhbmRlZCA9IHRydWUKICAgICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSByZXNwb25zZS5lcnJvcnMgfHwgW10KCiAgICAgICAgICAvLyDmmL7npLror6bnu4bnmoTop6PmnpDnu5PmnpwKICAgICAgICAgIGNvbnN0IGVycm9yQ291bnQgPSByZXNwb25zZS5lcnJvcnMgPyByZXNwb25zZS5lcnJvcnMubGVuZ3RoIDogMAogICAgICAgICAgaWYgKGVycm9yQ291bnQgPiAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf6Kej5p6Q5Ye6ICR7cmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu77yM5pyJICR7ZXJyb3JDb3VudH0g5Liq6ZSZ6K+v5oiW6K2m5ZGKYCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf6Kej5p6Q5Ye6ICR7cmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgICAgIH0KCgogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrop6PmnpDlh7rku7vkvZXpopjnm67vvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8nKQogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmVycm9ycyB8fCBbJ+acquiDveino+aekOWHuumimOebruWGheWuuSddCgoKICAgICAgICB9CgogICAgICAgIC8vIOWwhuWOn+Wni+WGheWuueWhq+WFheWIsOWvjOaWh+acrOe8lui+keWZqOS4rQogICAgICAgIGlmIChyZXNwb25zZS5vcmlnaW5hbENvbnRlbnQpIHsKICAgICAgICAgIHRoaXMuc2V0RWRpdG9yQ29udGVudChyZXNwb25zZS5vcmlnaW5hbENvbnRlbnQpCiAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudAogICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gcmVzcG9uc2Uub3JpZ2luYWxDb250ZW50IC8vIOWIneWni+WMlkhUTUzlhoXlrrkKCiAgICAgICAgfQoKICAgICAgICAvLyDlu7bov5/ph43nva7moIflv5fkvY3vvIznoa7kv53miYDmnInlvILmraXmk43kvZzlrozmiJAKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSBmYWxzZQogICAgICAgIH0sIDIwMDApCiAgICAgIH0gZWxzZSB7CgogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofku7bkuIrkvKDlpLHotKUnKQogICAgICAgIC8vIOmHjee9rueKtuaAgQogICAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKCiAgICAvLyDkuIrkvKDlpLHotKUKICAgIGhhbmRsZVVwbG9hZEVycm9yKCkgewogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqXmiJbogZTns7vnrqHnkIblkZgnKQoKICAgICAgLy8g6YeN572u54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICB9LAoKCgogICAgLy8g5YiH5o2i6aKY55uu5bGV5byAL+aUtui1twogICAgdG9nZ2xlUXVlc3Rpb24oaW5kZXgpIHsKICAgICAgY29uc3QgcXVlc3Rpb24gPSB0aGlzLnBhcnNlZFF1ZXN0aW9uc1tpbmRleF0KICAgICAgdGhpcy4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgIXF1ZXN0aW9uLmNvbGxhcHNlZCkKICAgIH0sCgogICAgLy8g5YWo6YOo5bGV5byAL+aUtui1twogICAgdG9nZ2xlQWxsUXVlc3Rpb25zKCkgewogICAgICB0aGlzLmFsbEV4cGFuZGVkID0gIXRoaXMuYWxsRXhwYW5kZWQKICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgdGhpcy4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgIXRoaXMuYWxsRXhwYW5kZWQpCiAgICAgIH0pCgogICAgfSwKCiAgICAvLyDnoa7orqTlr7zlhaUKICAgIGNvbmZpcm1JbXBvcnQoKSB7CiAgICAgIGlmICh0aGlzLnBhcnNlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+WvvOWFpeeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOaehOW7uuehruiupOS/oeaBrwogICAgICBsZXQgY29uZmlybU1lc3NhZ2UgPSBg56Gu6K6k5a+85YWlICR7dGhpcy5wYXJzZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67lkJfvvJ9gCiAgICAgIGxldCBvcHRpb25NZXNzYWdlcyA9IFtdCgogICAgICBpZiAodGhpcy5pbXBvcnRPcHRpb25zLnJldmVyc2UpIHsKICAgICAgICBvcHRpb25NZXNzYWdlcy5wdXNoKCflsIbmjInlgJLluo/lr7zlhaUnKQogICAgICB9CiAgICAgIGlmICh0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUpIHsKICAgICAgICBvcHRpb25NZXNzYWdlcy5wdXNoKCflhYHorrjph43lpI3popjnm64nKQogICAgICB9CgogICAgICBpZiAob3B0aW9uTWVzc2FnZXMubGVuZ3RoID4gMCkgewogICAgICAgIGNvbmZpcm1NZXNzYWdlICs9IGBcblxu5a+85YWl6YCJ6aG577yaJHtvcHRpb25NZXNzYWdlcy5qb2luKCfvvIwnKX1gCiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oY29uZmlybU1lc3NhZ2UsICfnoa7orqTlr7zlhaUnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrprlr7zlhaUnLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IGZhbHNlCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuaW1wb3J0UXVlc3Rpb25zKCkKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICB9LAoKICAgIC8vIOWvvOWFpemimOebrgogICAgYXN5bmMgaW1wb3J0UXVlc3Rpb25zKCkgewogICAgICB0aGlzLmltcG9ydGluZ1F1ZXN0aW9ucyA9IHRydWUKICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDAKCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aSE55CG5a+85YWl6YCJ6aG5CiAgICAgICAgbGV0IHF1ZXN0aW9uc1RvSW1wb3J0ID0gWy4uLnRoaXMucGFyc2VkUXVlc3Rpb25zXQoKICAgICAgICBpZiAodGhpcy5pbXBvcnRPcHRpb25zLnJldmVyc2UpIHsKICAgICAgICAgIHF1ZXN0aW9uc1RvSW1wb3J0LnJldmVyc2UoKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmjInlgJLluo/mjpLliJfpopjnm64nKQogICAgICAgIH0KCiAgICAgICAgLy8g5qih5ouf6L+b5bqm5pu05pawCiAgICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDEwCgogICAgICAgIC8vIOiwg+eUqOWunumZheeahOWvvOWFpUFQSQogICAgICAgIGNvbnN0IGltcG9ydERhdGEgPSB7CiAgICAgICAgICBiYW5rSWQ6IHRoaXMuYmFua0lkLAogICAgICAgICAgcXVlc3Rpb25zOiBxdWVzdGlvbnNUb0ltcG9ydCwKICAgICAgICAgIGFsbG93RHVwbGljYXRlOiB0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUsCiAgICAgICAgICByZXZlcnNlOiB0aGlzLmltcG9ydE9wdGlvbnMucmV2ZXJzZQogICAgICAgIH0KCiAgICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDMwCgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYmF0Y2hJbXBvcnRRdWVzdGlvbnMoaW1wb3J0RGF0YSkKCiAgICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDgwCgogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuaW1wb3J0UHJvZ3Jlc3MgPSAxMDAKCiAgICAgICAgICAvLyDmmL7npLror6bnu4bnmoTlr7zlhaXnu5PmnpwKICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3BvbnNlLmRhdGEgfHwge30KICAgICAgICAgIGNvbnN0IHN1Y2Nlc3NDb3VudCA9IHJlc3VsdC5zdWNjZXNzQ291bnQgfHwgMAogICAgICAgICAgY29uc3QgZmFpbENvdW50ID0gcmVzdWx0LmZhaWxDb3VudCB8fCAwCiAgICAgICAgICBjb25zdCBza2lwcGVkQ291bnQgPSByZXN1bHQuc2tpcHBlZENvdW50IHx8IDAKCiAgICAgICAgICAvLyDmnoTlu7rnu5Pmnpzmtojmga8KICAgICAgICAgIGxldCByZXN1bHRNZXNzYWdlID0gYOWvvOWFpeWujOaIkO+8muaIkOWKnyAke3N1Y2Nlc3NDb3VudH0g6YGTYAoKICAgICAgICAgIGlmIChmYWlsQ291bnQgPiAwKSB7CiAgICAgICAgICAgIHJlc3VsdE1lc3NhZ2UgKz0gYO+8jOWksei0pSAke2ZhaWxDb3VudH0g6YGTYAogICAgICAgICAgfQoKICAgICAgICAgIGlmIChza2lwcGVkQ291bnQgPiAwKSB7CiAgICAgICAgICAgIHJlc3VsdE1lc3NhZ2UgKz0gYO+8jOi3s+i/h+mHjeWkjSAke3NraXBwZWRDb3VudH0g6YGTYAogICAgICAgICAgfQoKICAgICAgICAgIHJlc3VsdE1lc3NhZ2UgKz0gJyDpopjnm64nCgogICAgICAgICAgLy8g5qC55o2u57uT5p6c57G75Z6L5pi+56S65LiN5ZCM55qE5raI5oGvCiAgICAgICAgICBpZiAoZmFpbENvdW50ID4gMCB8fCBza2lwcGVkQ291bnQgPiAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXN1bHRNZXNzYWdlKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlc3VsdE1lc3NhZ2UpCiAgICAgICAgICB9CgogICAgICAgICAgLy8g5aaC5p6c5pyJ6ZSZ6K+v5L+h5oGv77yM5pi+56S66K+m5oOFCiAgICAgICAgICBpZiAocmVzdWx0LmVycm9ycyAmJiByZXN1bHQuZXJyb3JzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCflr7zlhaXor6bmg4U6JywgcmVzdWx0LmVycm9ycykKCiAgICAgICAgICAgIC8vIOWmguaenOaciei3s+i/h+eahOmimOebru+8jOWPr+S7peaYvuekuuabtOivpue7hueahOS/oeaBrwogICAgICAgICAgICBpZiAoc2tpcHBlZENvdW50ID4gMCkgewogICAgICAgICAgICAgIGNvbnN0IHNraXBwZWRFcnJvcnMgPSByZXN1bHQuZXJyb3JzLmZpbHRlcihlcnJvciA9PiBlcnJvci5pbmNsdWRlcygn6YeN5aSN6Lez6L+HJykpCiAgICAgICAgICAgICAgaWYgKHNraXBwZWRFcnJvcnMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgY29uc29sZS5pbmZvKCfot7Pov4fnmoTph43lpI3popjnm646Jywgc2tpcHBlZEVycm9ycykKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5a+85YWl5aSx6LSlJykKICAgICAgICB9CgogICAgICAgIC8vIOa4heeQhueKtuaAgeW5tuWFs+mXreaKveWxiQogICAgICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSAnJwogICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9ICcnCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXQoKICAgICAgICAvLyDliLfmlrDmlbDmja4KICAgICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5a+85YWl6aKY55uu5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmltcG9ydGluZ1F1ZXN0aW9ucyA9IGZhbHNlCiAgICAgICAgdGhpcy5pbXBvcnRQcm9ncmVzcyA9IDAKICAgICAgfQogICAgfSwKCiAgICAvLyDmoLzlvI/ljJbov5vluqbmmL7npLoKICAgIGZvcm1hdFByb2dyZXNzKHBlcmNlbnRhZ2UpIHsKICAgICAgaWYgKHBlcmNlbnRhZ2UgPT09IDEwMCkgewogICAgICAgIHJldHVybiAn5a+85YWl5a6M5oiQJwogICAgICB9IGVsc2UgaWYgKHBlcmNlbnRhZ2UgPj0gODApIHsKICAgICAgICByZXR1cm4gJ+ato+WcqOS/neWtmC4uLicKICAgICAgfSBlbHNlIGlmIChwZXJjZW50YWdlID49IDMwKSB7CiAgICAgICAgcmV0dXJuICfmraPlnKjlpITnkIYuLi4nCiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICflh4blpIfkuK0uLi4nCiAgICAgIH0KICAgIH0sCgogICAgLy8g5Yid5aeL5YyW5a+M5paH5pys57yW6L6R5ZmoCiAgICBpbml0UmljaEVkaXRvcigpIHsKICAgICAgaWYgKHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5qOA5p+lQ0tFZGl0b3LmmK/lkKblj6/nlKgKICAgICAgaWYgKCF3aW5kb3cuQ0tFRElUT1IpIHsKICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo5bey5a2Y5Zyo77yM5YWI6ZSA5q+BCiAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgICAgIH0KCiAgICAgICAgLy8g56Gu5L+d5a655Zmo5a2Y5ZyoCiAgICAgICAgY29uc3QgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJykKICAgICAgICBpZiAoIWVkaXRvckNvbnRhaW5lcikgewogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICAvLyDliJvlu7p0ZXh0YXJlYeWFg+e0oAogICAgICAgIGVkaXRvckNvbnRhaW5lci5pbm5lckhUTUwgPSAnPHRleHRhcmVhIGlkPSJyaWNoLWVkaXRvci10ZXh0YXJlYSIgbmFtZT0icmljaC1lZGl0b3ItdGV4dGFyZWEiPjwvdGV4dGFyZWE+JwoKICAgICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7liJvlu7rnvJbovpHlmagKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAvLyDmo4Dmn6VDS0VkaXRvcuaYr+WQpuWPr+eUqAogICAgICAgICAgaWYgKCF3aW5kb3cuQ0tFRElUT1IgfHwgIXdpbmRvdy5DS0VESVRPUi5yZXBsYWNlKSB7CgogICAgICAgICAgICB0aGlzLnNob3dGYWxsYmFja0VkaXRvciA9IHRydWUKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CgogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgLy8g5YWI5bCd6K+V5a6M5pW06YWN572uCiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvciA9IHdpbmRvdy5DS0VESVRPUi5yZXBsYWNlKCdyaWNoLWVkaXRvci10ZXh0YXJlYScsIHsKICAgICAgICAgICAgICBoZWlnaHQ6ICdjYWxjKDEwMHZoIC0gMjAwcHgpJywgLy8g5YWo5bGP6auY5bqm5YeP5Y675aS06YOo5ZKM5YW25LuW5YWD57Sg55qE6auY5bqmCiAgICAgICAgICAgICAgdG9vbGJhcjogWwogICAgICAgICAgICAgICAgeyBuYW1lOiAnc3R5bGVzJywgaXRlbXM6IFsnRm9udFNpemUnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnYmFzaWNzdHlsZXMnLCBpdGVtczogWydCb2xkJywgJ0l0YWxpYycsICdVbmRlcmxpbmUnLCAnU3RyaWtlJywgJ1N1cGVyc2NyaXB0JywgJ1N1YnNjcmlwdCcsICctJywgJ1JlbW92ZUZvcm1hdCddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdjbGlwYm9hcmQnLCBpdGVtczogWydDdXQnLCAnQ29weScsICdQYXN0ZScsICdQYXN0ZVRleHQnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnY29sb3JzJywgaXRlbXM6IFsnVGV4dENvbG9yJywgJ0JHQ29sb3InXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAncGFyYWdyYXBoJywgaXRlbXM6IFsnSnVzdGlmeUxlZnQnLCAnSnVzdGlmeUNlbnRlcicsICdKdXN0aWZ5UmlnaHQnLCAnSnVzdGlmeUJsb2NrJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2VkaXRpbmcnLCBpdGVtczogWydVbmRvJywgJ1JlZG8nXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnbGlua3MnLCBpdGVtczogWydMaW5rJywgJ1VubGluayddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdpbnNlcnQnLCBpdGVtczogWydJbWFnZScsICdTcGVjaWFsQ2hhciddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICd0b29scycsIGl0ZW1zOiBbJ01heGltaXplJ10gfQogICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgcmVtb3ZlQnV0dG9uczogJycsCiAgICAgICAgICAgICAgbGFuZ3VhZ2U6ICd6aC1jbicsCiAgICAgICAgICAgICAgcmVtb3ZlUGx1Z2luczogJ2VsZW1lbnRzcGF0aCcsCiAgICAgICAgICAgICAgcmVzaXplX2VuYWJsZWQ6IGZhbHNlLAogICAgICAgICAgICAgIGV4dHJhUGx1Z2luczogJ2ZvbnQsY29sb3JidXR0b24sanVzdGlmeSxzcGVjaWFsY2hhcixpbWFnZScsCiAgICAgICAgICAgICAgYWxsb3dlZENvbnRlbnQ6IHRydWUsCiAgICAgICAgICAgICAgLy8g5a2X5L2T5aSn5bCP6YWN572uCiAgICAgICAgICAgICAgZm9udFNpemVfc2l6ZXM6ICcxMi8xMnB4OzE0LzE0cHg7MTYvMTZweDsxOC8xOHB4OzIwLzIwcHg7MjIvMjJweDsyNC8yNHB4OzI2LzI2cHg7MjgvMjhweDszNi8zNnB4OzQ4LzQ4cHg7NzIvNzJweCcsCiAgICAgICAgICAgICAgZm9udFNpemVfZGVmYXVsdExhYmVsOiAnMTRweCcsCiAgICAgICAgICAgICAgLy8g6aKc6Imy6YWN572uCiAgICAgICAgICAgICAgY29sb3JCdXR0b25fZW5hYmxlTW9yZTogdHJ1ZSwKICAgICAgICAgICAgICBjb2xvckJ1dHRvbl9jb2xvcnM6ICdDRjVENEUsNDU0NTQ1LEZGRixDQ0MsRERELENDRUFFRSw2NkFCMTYnLAogICAgICAgICAgICAgIC8vIOWbvuWDj+S4iuS8oOmFjee9riAtIOWPguiAg+aCqOaPkOS+m+eahOagh+WHhumFjee9rgogICAgICAgICAgICAgIGZpbGVicm93c2VyVXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy9jb21tb24vdXBsb2FkSW1hZ2UnLAogICAgICAgICAgICAgIGltYWdlX3ByZXZpZXdUZXh0OiAnICcsCiAgICAgICAgICAgICAgLy8g6K6+572u5Z+656GA6Lev5b6E77yM6K6p55u45a+56Lev5b6E6IO95q2j56Gu6Kej5p6Q5Yiw5ZCO56uv5pyN5Yqh5ZmoCiAgICAgICAgICAgICAgYmFzZUhyZWY6ICdodHRwOi8vbG9jYWxob3N0Ojg4MDIvJywKICAgICAgICAgICAgICAvLyDlm77lg4/mj5LlhaXphY3nva4KICAgICAgICAgICAgICBpbWFnZV9wcmV2aWV3VGV4dDogJ+mihOiniOWMuuWfnycsCiAgICAgICAgICAgICAgaW1hZ2VfcmVtb3ZlTGlua0J5RW1wdHlVUkw6IHRydWUsCiAgICAgICAgICAgICAgLy8g6ZqQ6JeP5LiN6ZyA6KaB55qE5qCH562+6aG177yM5Y+q5L+d55WZ5LiK5Lyg5ZKM5Zu+5YOP5L+h5oGvCiAgICAgICAgICAgICAgcmVtb3ZlRGlhbG9nVGFiczogJ2ltYWdlOkxpbms7aW1hZ2U6YWR2YW5jZWQnLAogICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICBpbnN0YW5jZVJlYWR5OiBmdW5jdGlvbihldnQpIHsKICAgICAgICAgICAgICAgICAgY29uc3QgZWRpdG9yID0gZXZ0LmVkaXRvcgogICAgICAgICAgICAgICAgICBlZGl0b3Iub24oJ2RpYWxvZ1Nob3cnLCBmdW5jdGlvbihldnQpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWFsb2cgPSBldnQuZGF0YQogICAgICAgICAgICAgICAgICAgIGlmIChkaWFsb2cuZ2V0TmFtZSgpID09PSAnaW1hZ2UnKSB7CiAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXJsRmllbGQgPSBkaWFsb2cuZ2V0Q29udGVudEVsZW1lbnQoJ2luZm8nLCAndHh0VXJsJykKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh1cmxGaWVsZCAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpICYmIHVybEZpZWxkLmdldFZhbHVlKCkuc3RhcnRzV2l0aCgnLycpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSwgNTAwKQogICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCksIDEwMDAwKQogICAgICAgICAgICAgICAgICAgICAgfSwgMTAwMCkKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKQogICAgICAgICAgICByZXR1cm4KICAgICAgICAgIH0KCiAgICAgICAgICAvLyDnm5HlkKzlhoXlrrnlj5jljJYgLSDkvb/nlKjpmLLmipbkvJjljJbmgKfog70KICAgICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5yaWNoRWRpdG9yLm9uKSB7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5vbignY2hhbmdlJywgKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlKCkKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5vbigna2V5JywgKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuZGVib3VuY2VFZGl0b3JDb250ZW50Q2hhbmdlKCkKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5vbignaW5zdGFuY2VSZWFkeScsICgpID0+IHsKICAgICAgICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZQogICAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKCcnKQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuZmFsbGJhY2tUb1RleHRhcmVhKCkKICAgICAgfQogICAgfSwKCiAgICAvLyDlpITnkIbnvJbovpHlmajlhoXlrrnlj5jljJbvvIjpmLLmipblkI7miafooYzvvIkKICAgIGhhbmRsZUVkaXRvckNvbnRlbnRDaGFuZ2VEZWJvdW5jZWQoKSB7CiAgICAgIGlmICghdGhpcy5yaWNoRWRpdG9yIHx8ICF0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmF3Q29udGVudCA9IHRoaXMucmljaEVkaXRvci5nZXREYXRhKCkKICAgICAgICBjb25zdCBjb250ZW50V2l0aFJlbGF0aXZlVXJscyA9IHRoaXMuY29udmVydFVybHNUb1JlbGF0aXZlKHJhd0NvbnRlbnQpCiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gdGhpcy5wcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyhjb250ZW50V2l0aFJlbGF0aXZlVXJscykKICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS53YXJuKCfnvJbovpHlmajlhoXlrrnlpITnkIblpLHotKU6JywgZXJyb3IpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5Zue6YCA5Yiw5pmu6YCa5paH5pys5qGGCiAgICBmYWxsYmFja1RvVGV4dGFyZWEoKSB7CiAgICAgIGNvbnN0IGVkaXRvckNvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyaWNoLWVkaXRvcicpCiAgICAgIGlmIChlZGl0b3JDb250YWluZXIpIHsKICAgICAgICBjb25zdCB0ZXh0YXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJykKICAgICAgICB0ZXh0YXJlYS5jbGFzc05hbWUgPSAnZmFsbGJhY2stdGV4dGFyZWEnCiAgICAgICAgdGV4dGFyZWEucGxhY2Vob2xkZXIgPSAn6K+35Zyo5q2k5aSE57KY6LS05oiW6L6T5YWl6aKY55uu5YaF5a65Li4uJwogICAgICAgIHRleHRhcmVhLnZhbHVlID0gJycgLy8g56Gu5L+d5paH5pys5qGG5Li656m6CiAgICAgICAgdGV4dGFyZWEuc3R5bGUuY3NzVGV4dCA9ICd3aWR0aDogMTAwJTsgaGVpZ2h0OiA0MDBweDsgYm9yZGVyOiAxcHggc29saWQgI2RkZDsgcGFkZGluZzogMTBweDsgZm9udC1mYW1pbHk6ICJDb3VyaWVyIE5ldyIsIG1vbm9zcGFjZTsgZm9udC1zaXplOiAxNHB4OyBsaW5lLWhlaWdodDogMS42OyByZXNpemU6IG5vbmU7JwoKICAgICAgICAvLyDnm5HlkKzlhoXlrrnlj5jljJYgLSDkvb/nlKjpmLLmipbkvJjljJbmgKfog70KICAgICAgICB0ZXh0YXJlYS5hZGRFdmVudExpc3RlbmVyKCdpbnB1dCcsIChlKSA9PiB7CiAgICAgICAgICAvLyDnq4vljbPmm7TmlrDlhoXlrrnvvIzkvYbpmLLmipbop6PmnpAKICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gZS50YXJnZXQudmFsdWUKICAgICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9IGUudGFyZ2V0LnZhbHVlCiAgICAgICAgfSkKCiAgICAgICAgZWRpdG9yQ29udGFpbmVyLmlubmVySFRNTCA9ICcnCiAgICAgICAgZWRpdG9yQ29udGFpbmVyLmFwcGVuZENoaWxkKHRleHRhcmVhKQogICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSB0cnVlCiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDorr7nva7nvJbovpHlmajlhoXlrrkKICAgIHNldEVkaXRvckNvbnRlbnQoY29udGVudCkgewogICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICB0aGlzLnJpY2hFZGl0b3Iuc2V0RGF0YShjb250ZW50KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gY29udGVudAogICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9IGNvbnRlbnQKICAgICAgfQogICAgfSwKCgoKICAgIC8vIOmYsuaKluWHveaVsCAtIOS8mOWMlueJiOacrO+8jOaUr+aMgeWPlua2iAogICAgZGVib3VuY2UoZnVuYywgd2FpdCkgewogICAgICBsZXQgdGltZW91dAogICAgICBjb25zdCBkZWJvdW5jZWQgPSBmdW5jdGlvbiBleGVjdXRlZEZ1bmN0aW9uKC4uLmFyZ3MpIHsKICAgICAgICBjb25zdCBsYXRlciA9ICgpID0+IHsKICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KQogICAgICAgICAgdGltZW91dCA9IG51bGwKICAgICAgICAgIGZ1bmMuYXBwbHkodGhpcywgYXJncykKICAgICAgICB9CiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgdGltZW91dCA9IHNldFRpbWVvdXQobGF0ZXIsIHdhaXQpCiAgICAgIH0KCiAgICAgIC8vIOa3u+WKoOWPlua2iOaWueazlQogICAgICBkZWJvdW5jZWQuY2FuY2VsID0gZnVuY3Rpb24oKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgdGltZW91dCA9IG51bGwKICAgICAgfQoKICAgICAgcmV0dXJuIGRlYm91bmNlZAogICAgfSwKCiAgICAvLyDlsIbnvJbovpHlmajlhoXlrrnkuK3nmoTlrozmlbRVUkzovazmjaLkuLrnm7jlr7not6/lvoQKICAgIGNvbnZlcnRVcmxzVG9SZWxhdGl2ZShjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCkgcmV0dXJuIGNvbnRlbnQKCiAgICAgIC8vIOWMuemFjeW9k+WJjeWfn+WQjeeahOWujOaVtFVSTOW5tui9rOaNouS4uuebuOWvuei3r+W+hAogICAgICBjb25zdCBjdXJyZW50T3JpZ2luID0gd2luZG93LmxvY2F0aW9uLm9yaWdpbgogICAgICBjb25zdCB1cmxSZWdleCA9IG5ldyBSZWdFeHAoY3VycmVudE9yaWdpbi5yZXBsYWNlKC9bLiorP14ke30oKXxbXF1cXF0vZywgJ1xcJCYnKSArICcoL1teIlwnXFxzPl0qKScsICdnJykKCiAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UodXJsUmVnZXgsICckMScpCiAgICB9LAoKICAgIC8vIOino+aekOaWh+ahowogICAgcGFyc2VEb2N1bWVudCgpIHsKICAgICAgaWYgKCF0aGlzLmRvY3VtZW50Q29udGVudC50cmltKCkpIHsKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcGFyc2VSZXN1bHQgPSB0aGlzLnBhcnNlUXVlc3Rpb25Db250ZW50KHRoaXMuZG9jdW1lbnRDb250ZW50KQogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gcGFyc2VSZXN1bHQucXVlc3Rpb25zLm1hcChxdWVzdGlvbiA9PiAoewogICAgICAgICAgLi4ucXVlc3Rpb24sCiAgICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlCiAgICAgICAgfSkpCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHBhcnNlUmVzdWx0LmVycm9ycwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbJ+ino+aekOWksei0pe+8micgKyBlcnJvci5tZXNzYWdlXQogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgfQogICAgfSwKCiAgICAvLyDop6PmnpDpopjnm67lhoXlrrkgLSDkvJjljJbniYjmnKzvvIzmm7TliqDlgaXlo64KICAgIHBhcnNlUXVlc3Rpb25Db250ZW50KGNvbnRlbnQpIHsKICAgICAgY29uc3QgcXVlc3Rpb25zID0gW10KICAgICAgY29uc3QgZXJyb3JzID0gW10KCiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKCiAgICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnM6IFsn6Kej5p6Q5YaF5a655Li656m65oiW5qC85byP5LiN5q2j56GuJ10gfQogICAgICB9CgogICAgICB0cnkgewoKCiAgICAgICAgY29uc3QgdGV4dENvbnRlbnQgPSB0aGlzLnN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnQpCgogICAgICAgIGlmICghdGV4dENvbnRlbnQgfHwgdGV4dENvbnRlbnQudHJpbSgpLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnM6IFsn5aSE55CG5ZCO55qE5YaF5a655Li656m6J10gfQogICAgICAgIH0KCiAgICAgICAgY29uc3QgbGluZXMgPSB0ZXh0Q29udGVudC5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKGxpbmUgPT4gbGluZS5sZW5ndGggPiAwKQoKICAgICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyfmsqHmnInmnInmlYjnmoTlhoXlrrnooYwnXSB9CiAgICAgICAgfQoKCgogICAgICAgIGxldCBjdXJyZW50UXVlc3Rpb25MaW5lcyA9IFtdCiAgICAgICAgbGV0IHF1ZXN0aW9uTnVtYmVyID0gMAoKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/popjnm67lvIDlp4vooYzvvJrmlbDlrZfjgIFb6aKY55uu57G75Z6LXSDmiJYgW+mimOebruexu+Wei10KICAgICAgICAgIGNvbnN0IGlzUXVlc3Rpb25TdGFydCA9IHRoaXMuaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSB8fCB0aGlzLmlzUXVlc3Rpb25UeXBlU3RhcnQobGluZSkKCiAgICAgICAgICBpZiAoaXNRdWVzdGlvblN0YXJ0KSB7CiAgICAgICAgICAgIC8vIOWmguaenOS5i+WJjeaciemimOebruWGheWuue+8jOWFiOWkhOeQhuS5i+WJjeeahOmimOebrgogICAgICAgICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCBxdWVzdGlvblRleHQgPSBjdXJyZW50UXVlc3Rpb25MaW5lcy5qb2luKCdcbicpCiAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWRRdWVzdGlvbiA9IHRoaXMucGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQsIHF1ZXN0aW9uTnVtYmVyKQogICAgICAgICAgICAgICAgaWYgKHBhcnNlZFF1ZXN0aW9uKSB7CiAgICAgICAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKHBhcnNlZFF1ZXN0aW9uKQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBlcnJvcnMucHVzaChg56ysICR7cXVlc3Rpb25OdW1iZXJ9IOmimOino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDlvIDlp4vmlrDpopjnm64KICAgICAgICAgICAgY3VycmVudFF1ZXN0aW9uTGluZXMgPSBbbGluZV0KICAgICAgICAgICAgcXVlc3Rpb25OdW1iZXIrKwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5aaC5p6c5b2T5YmN5Zyo5aSE55CG6aKY55uu5Lit77yM5re75Yqg5Yiw5b2T5YmN6aKY55uuCiAgICAgICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgY3VycmVudFF1ZXN0aW9uTGluZXMucHVzaChsaW5lKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAvLyDlpITnkIbmnIDlkI7kuIDkuKrpopjnm64KICAgICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgY29uc3QgcXVlc3Rpb25UZXh0ID0gY3VycmVudFF1ZXN0aW9uTGluZXMuam9pbignXG4nKQogICAgICAgICAgICBjb25zdCBwYXJzZWRRdWVzdGlvbiA9IHRoaXMucGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQsIHF1ZXN0aW9uTnVtYmVyKQogICAgICAgICAgICBpZiAocGFyc2VkUXVlc3Rpb24pIHsKICAgICAgICAgICAgICBxdWVzdGlvbnMucHVzaChwYXJzZWRRdWVzdGlvbikKICAgICAgICAgICAgfQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgZXJyb3JzLnB1c2goYOesrCAke3F1ZXN0aW9uTnVtYmVyfSDpopjop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgZXJyb3JzLnB1c2goYOaWh+aho+ino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCiAgICAgIH0KCiAgICAgIHJldHVybiB7IHF1ZXN0aW9ucywgZXJyb3JzIH0KICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66aKY55uu5byA5aeL6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya5q+P6aKY5YmN6Z2i6ZyA6KaB5Yqg5LiK6aKY5Y+35qCH6K+G77yM6aKY5Y+35ZCO6Z2i6ZyA6KaB5Yqg5LiK56ym5Y+377yIOu+8muOAgS7vvI7vvIkKICAgICAgLy8g5Yy56YWN5qC85byP77ya5pWw5a2XICsg56ym5Y+3KDrvvJrjgIEu77yOKSArIOWPr+mAieepuuagvAogICAgICAvLyDkvovlpoLvvJoxLiAx44CBIDHvvJogMe+8jiDnrYkKICAgICAgcmV0dXJuIC9eXGQrWy4677ya77yO44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpopjlnovmoIfms6jlvIDlp4vooYwKICAgIGlzUXVlc3Rpb25UeXBlU3RhcnQobGluZSkgewogICAgICAvLyDljLnphY3moLzlvI/vvJpb6aKY55uu57G75Z6LXQogICAgICAvLyDkvovlpoLvvJpb5Y2V6YCJ6aKYXSBb5aSa6YCJ6aKYXSBb5Yik5pat6aKYXSDnrYkKICAgICAgcmV0dXJuIC9eXFsuKj/pophcXS8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDku47ooYzmlbDnu4Top6PmnpDljZXkuKrpopjnm64gLSDmjInnhafovpPlhaXop4TojIMKICAgIHBhcnNlUXVlc3Rpb25Gcm9tTGluZXMocXVlc3Rpb25UZXh0KSB7CiAgICAgIGNvbnN0IGxpbmVzID0gcXVlc3Rpb25UZXh0LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfpopjnm67lhoXlrrnkuLrnqbonKQogICAgICB9CgogICAgICBsZXQgcXVlc3Rpb25UeXBlID0gJ2p1ZGdtZW50JyAvLyDpu5jorqTliKTmlq3popgKICAgICAgbGV0IHF1ZXN0aW9uQ29udGVudCA9ICcnCiAgICAgIGxldCBjb250ZW50U3RhcnRJbmRleCA9IDAKCiAgICAgIC8vIOajgOafpeaYr+WQpuaciemimOWei+agh+azqO+8iOWmgiBb5Y2V6YCJ6aKYXeOAgVvlpJrpgInpophd44CBW+WIpOaWremimF3vvIkKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQogICAgICAgIGNvbnN0IHR5cGVNYXRjaCA9IGxpbmUubWF0Y2goL1xbKC4qP+mimClcXS8pCiAgICAgICAgaWYgKHR5cGVNYXRjaCkgewogICAgICAgICAgY29uc3QgdHlwZVRleHQgPSB0eXBlTWF0Y2hbMV0KCiAgICAgICAgICAvLyDovazmjaLpopjnm67nsbvlnosKICAgICAgICAgIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5Yik5patJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2p1ZGdtZW50JwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5Y2V6YCJJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ3NpbmdsZScKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+WkmumAiScpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZScKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+Whq+epuicpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdmaWxsJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn566A562UJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2Vzc2F5JwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOWmguaenOmimOWei+agh+azqOWSjOmimOebruWGheWuueWcqOWQjOS4gOihjAogICAgICAgICAgY29uc3QgcmVtYWluaW5nQ29udGVudCA9IGxpbmUucmVwbGFjZSgvXFsuKj/pophcXS8sICcnKS50cmltKCkKICAgICAgICAgIGlmIChyZW1haW5pbmdDb250ZW50KSB7CiAgICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IHJlbWFpbmluZ0NvbnRlbnQKICAgICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOmimOWei+agh+azqO+8jOS7juesrOS4gOihjOW8gOWni+ino+aekAogICAgICBpZiAoY29udGVudFN0YXJ0SW5kZXggPT09IDApIHsKICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IDAKICAgICAgfQoKICAgICAgLy8g5o+Q5Y+W6aKY55uu5YaF5a6577yI5LuO6aKY5Y+36KGM5byA5aeL77yJCiAgICAgIGZvciAobGV0IGkgPSBjb250ZW50U3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgIC8vIOWmguaenOaYr+mimOWPt+ihjO+8jOaPkOWPlumimOebruWGheWuue+8iOenu+mZpOmimOWPt++8iQogICAgICAgIGlmICh0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkpIHsKICAgICAgICAgIC8vIOenu+mZpOmimOWPt++8jOaPkOWPlumimOebruWGheWuuQogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gbGluZS5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIGJyZWFrCiAgICAgICAgfSBlbHNlIGlmICghcXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgICAvLyDlpoLmnpzov5jmsqHmnInpopjnm67lhoXlrrnvvIzlvZPliY3ooYzlsLHmmK/popjnm67lhoXlrrkKICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IGxpbmUKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDnu6fnu63mlLbpm4bpopjnm67lhoXlrrnvvIjnm7TliLDpgYfliLDpgInpobnmiJbnrZTmoYjvvIkKICAgICAgZm9yIChsZXQgaSA9IGNvbnRlbnRTdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g5aaC5p6c6YGH5Yiw6YCJ6aG56KGM44CB562U5qGI6KGM44CB6Kej5p6Q6KGM5oiW6Zq+5bqm6KGM77yM5YGc5q2i5pS26ZuG6aKY55uu5YaF5a65CiAgICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNBbnN3ZXJMaW5lKGxpbmUpIHx8CiAgICAgICAgICAgIHRoaXMuaXNFeHBsYW5hdGlvbkxpbmUobGluZSkgfHwgdGhpcy5pc0RpZmZpY3VsdHlMaW5lKGxpbmUpKSB7CiAgICAgICAgICBicmVhawogICAgICAgIH0KCiAgICAgICAgLy8g57un57ut5re75Yqg5Yiw6aKY55uu5YaF5a6577yM5L2G6KaB56Gu5L+d5LiN5YyF5ZCr6aKY5Y+3CiAgICAgICAgbGV0IGNsZWFuTGluZSA9IGxpbmUKICAgICAgICAvLyDlpoLmnpzov5nooYzov5jljIXlkKvpopjlj7fvvIznp7vpmaTlroMKICAgICAgICBpZiAodGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpKSB7CiAgICAgICAgICBjbGVhbkxpbmUgPSBsaW5lLnJlcGxhY2UoL15cZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICAgIH0KCiAgICAgICAgaWYgKGNsZWFuTGluZSkgewogICAgICAgICAgaWYgKHF1ZXN0aW9uQ29udGVudCkgewogICAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgKz0gJ1xuJyArIGNsZWFuTGluZQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gY2xlYW5MaW5lCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICBpZiAoIXF1ZXN0aW9uQ29udGVudCkgewogICAgICAgIHRocm93IG5ldyBFcnJvcign5peg5rOV5o+Q5Y+W6aKY55uu5YaF5a65JykKICAgICAgfQoKICAgICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655LiN5YyF5ZCr6aKY5Y+3CiAgICAgIGxldCBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IHF1ZXN0aW9uQ29udGVudC50cmltKCkKICAgICAgLy8g5L2/55So5pu05by655qE5riF55CG6YC76L6R77yM5aSa5qyh5riF55CG56Gu5L+d5b275bqV56e76Zmk6aKY5Y+3CiAgICAgIHdoaWxlICgvXlxzKlxkK1suOu+8mu+8juOAgV0vLnRlc3QoZmluYWxRdWVzdGlvbkNvbnRlbnQpKSB7CiAgICAgICAgZmluYWxRdWVzdGlvbkNvbnRlbnQgPSBmaW5hbFF1ZXN0aW9uQ29udGVudC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgfQoKICAgICAgLy8g6aKd5aSW5riF55CG77ya56e76Zmk5Y+v6IO955qESFRNTOagh+etvuWGheeahOmimOWPtwogICAgICBpZiAoZmluYWxRdWVzdGlvbkNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAgIGZpbmFsUXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihmaW5hbFF1ZXN0aW9uQ29udGVudCkKICAgICAgfQoKICAgICAgY29uc3QgcXVlc3Rpb24gPSB7CiAgICAgICAgcXVlc3Rpb25UeXBlOiBxdWVzdGlvblR5cGUsCiAgICAgICAgdHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgIHR5cGVOYW1lOiB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogZmluYWxRdWVzdGlvbkNvbnRlbnQsCiAgICAgICAgY29udGVudDogZmluYWxRdWVzdGlvbkNvbnRlbnQsCiAgICAgICAgZGlmZmljdWx0eTogJycsIC8vIOS4jeiuvue9rum7mOiupOWAvAogICAgICAgIGV4cGxhbmF0aW9uOiAnJywKICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICBjb3JyZWN0QW5zd2VyOiAnJywKICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlICAvLyDpu5jorqTlsZXlvIAKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q6YCJ6aG577yI5a+55LqO6YCJ5oup6aKY77yJCiAgICAgIGNvbnN0IG9wdGlvblJlc3VsdCA9IHRoaXMucGFyc2VPcHRpb25zRnJvbUxpbmVzKGxpbmVzLCAwKQogICAgICBxdWVzdGlvbi5vcHRpb25zID0gb3B0aW9uUmVzdWx0Lm9wdGlvbnMKCiAgICAgIC8vIOagueaNrumAiemhueaVsOmHj+aOqOaWremimOebruexu+Wei++8iOWmguaenOS5i+WJjeayoeacieaYjuehruagh+azqO+8iQogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnICYmIHF1ZXN0aW9uLm9wdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgIC8vIOWmguaenOaciemAiemhue+8jOaOqOaWreS4uumAieaLqemimAogICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdzaW5nbGUnICAvLyDpu5jorqTkuLrljZXpgInpopgKICAgICAgICBxdWVzdGlvbi5xdWVzdGlvblR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICBxdWVzdGlvbi50eXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgcXVlc3Rpb24udHlwZU5hbWUgPSB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpCiAgICAgIH0KCiAgICAgIC8vIOino+aekOetlOahiOOAgeino+aekOOAgemavuW6pgogICAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhRnJvbUxpbmVzKGxpbmVzLCBxdWVzdGlvbikKCiAgICAgIC8vIOagueaNruetlOahiOmVv+W6pui/m+S4gOatpeaOqOaWremAieaLqemimOexu+WeiwogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJyAmJiBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyICYmIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIubGVuZ3RoID4gMSkgewogICAgICAgIC8vIOWmguaenOetlOahiOWMheWQq+WkmuS4quWtl+avje+8jOaOqOaWreS4uuWkmumAiemimAogICAgICAgIGlmICgvXltBLVpdezIsfSQvLnRlc3QocXVlc3Rpb24uY29ycmVjdEFuc3dlcikpIHsKICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZScKICAgICAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgICAgcXVlc3Rpb24udHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgICAgcXVlc3Rpb24udHlwZU5hbWUgPSB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmnIDnu4jmuIXnkIbvvJrnoa7kv53popjnm67lhoXlrrnlrozlhajmsqHmnInpopjlj7flkozpopjlnovmoIfor4YKICAgICAgcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQpCiAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25UeXBlKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCkKICAgICAgcXVlc3Rpb24uY29udGVudCA9IHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudAoKICAgICAgcmV0dXJuIHF1ZXN0aW9uCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumAiemhueihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNPcHRpb25MaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya6YCJ6aG55qC85byP77yIQTrvvInvvIzlrZfmr43lj6/ku6XkuLpB5YiwWueahOS7u+aEj+Wkp+Wwj+WGmeWtl+avje+8jOWGkuWPt+WPr+S7peabv+aNouS4uiI677ya44CBLu+8jiLlhbbkuK3kuYvkuIAKICAgICAgLy8g5Lil5qC86aqM6K+B77ya6YG/5YWN6K+v5bCG6aKY55uu5YaF5a655Lit55qE5a2X5q+NK+espuWPt+ivhuWIq+S4uumAiemhuQogICAgICBpZiAoIWxpbmUgfHwgbGluZS5sZW5ndGggPiAyMDApIHsKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgY29uc3QgbWF0Y2ggPSBsaW5lLm1hdGNoKC9eKFtBLVphLXpdKVsuOu+8mu+8juOAgV1ccyooLiopLykKICAgICAgaWYgKG1hdGNoKSB7CiAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gbWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgIGNvbnN0IG9wdGlvbkNvbnRlbnQgPSBtYXRjaFsyXSA/IG1hdGNoWzJdLnRyaW0oKSA6ICcnCgogICAgICAgIC8vIOS4peagvOmqjOivgeadoeS7tu+8mgogICAgICAgIC8vIDEuIOmAiemhueWtl+avjeW/hemhu+aYr0EtWuWNleS4quWtl+avjQogICAgICAgIC8vIDIuIOmAiemhueWGheWuuemVv+W6puWQiOeQhu+8iDEtMTAw5a2X56ym77yJCiAgICAgICAgLy8gMy4g5o6S6Zmk5piO5pi+55qE6aKY55uu5YaF5a655o+P6L+w77yI5aaC5YyF5ZCrIuihqOekuiLjgIEi5pWw5o2uIuetieivjeaxh+eahOmVv+WPpe+8iQogICAgICAgIGlmICgvXltBLVpdJC8udGVzdChvcHRpb25LZXkpICYmIG9wdGlvbkNvbnRlbnQubGVuZ3RoID4gMCAmJiBvcHRpb25Db250ZW50Lmxlbmd0aCA8PSAxMDApIHsKICAgICAgICAgIC8vIOaOkumZpOaYjuaYvueahOmimOebruWGheWuueaPj+i/sAogICAgICAgICAgY29uc3QgZXhjbHVkZVBhdHRlcm5zID0gWwogICAgICAgICAgICAv6KGo56S6Lio/5pWw5o2uLywgICAgIC8vIOaOkumZpCLooajnpLouLi7mlbDmja4i6L+Z57G75o+P6L+wCiAgICAgICAgICAgIC/kuIDoiKznlKguKj/miJYvLCAgICAgIC8vIOaOkumZpCLkuIDoiKznlKguLi7miJYi6L+Z57G75o+P6L+wCiAgICAgICAgICAgIC/pgJrluLguKj/mnaUvLCAgICAgICAvLyDmjpLpmaQi6YCa5bi4Li4u5p2lIui/meexu+aPj+i/sAogICAgICAgICAgICAv5Y+v5LulLio/6L+b6KGMLywgICAgIC8vIOaOkumZpCLlj6/ku6UuLi7ov5vooYwi6L+Z57G75o+P6L+wCiAgICAgICAgICAgIC8uKj/lnZDmoIcuKj/ooajnpLovICAgLy8g5o6S6ZmkIuWdkOaghy4uLuihqOekuiLov5nnsbvmj4/ov7AKICAgICAgICAgIF0KCiAgICAgICAgICBjb25zdCBpc0Rlc2NyaXB0aXZlVGV4dCA9IGV4Y2x1ZGVQYXR0ZXJucy5zb21lKHBhdHRlcm4gPT4gcGF0dGVybi50ZXN0KG9wdGlvbkNvbnRlbnQpKQogICAgICAgICAgcmV0dXJuICFpc0Rlc2NyaXB0aXZlVGV4dAogICAgICAgIH0KICAgICAgfQogICAgICByZXR1cm4gZmFsc2UKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li6562U5qGI6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc0Fuc3dlckxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrmmL7lvI/moIfms6jmoLzlvI/vvIjnrZTmoYjvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXuetlOahiFsuOu+8muOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66Kej5p6Q6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc0V4cGxhbmF0aW9uTGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8muino+aekOagvOW8j++8iOino+aekO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e6Kej5p6QWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpmr7luqbooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzRGlmZmljdWx0eUxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrpmr7luqbmoLzlvI/vvIjpmr7luqbvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXumavuW6plsuOu+8muOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g6I635Y+W6aKY55uu57G75Z6L5pi+56S65ZCN56ewCiAgICBnZXRUeXBlRGlzcGxheU5hbWUodHlwZSkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnLAogICAgICAgICdzaW5nbGUnOiAn5Y2V6YCJ6aKYJywKICAgICAgICAnbXVsdGlwbGUnOiAn5aSa6YCJ6aKYJywKICAgICAgICAnZmlsbCc6ICfloavnqbrpopgnLAogICAgICAgICdlc3NheSc6ICfnroDnrZTpopgnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+WIpOaWremimCcKICAgIH0sCgogICAgLy8g5aSE55CG5Zu+54mH6Lev5b6E77yM5bCG55u45a+56Lev5b6E6L2s5o2i5Li65a6M5pW06Lev5b6ECiAgICBwcm9jZXNzSW1hZ2VQYXRocyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwcm9jZXNzZWRDb250ZW50ID0gY29udGVudC5yZXBsYWNlKC88aW1nKFtePl0qPylzcmM9IihbXiJdKj8pIihbXj5dKj8pPi9nLCAobWF0Y2gsIGJlZm9yZSwgc3JjLCBhZnRlcikgPT4gewogICAgICAgICAgaWYgKCFzcmMpIHJldHVybiBtYXRjaAoKICAgICAgICAgIGlmIChzcmMuc3RhcnRzV2l0aCgnaHR0cDovLycpIHx8IHNyYy5zdGFydHNXaXRoKCdodHRwczovLycpIHx8IHNyYy5zdGFydHNXaXRoKCdkYXRhOicpKSB7CiAgICAgICAgICAgIHJldHVybiBtYXRjaAogICAgICAgICAgfQoKICAgICAgICAgIGNvbnN0IGZ1bGxTcmMgPSAnaHR0cDovL2xvY2FsaG9zdDo4ODAyJyArIChzcmMuc3RhcnRzV2l0aCgnLycpID8gc3JjIDogJy8nICsgc3JjKQogICAgICAgICAgcmV0dXJuIGA8aW1nJHtiZWZvcmV9c3JjPSIke2Z1bGxTcmN9IiR7YWZ0ZXJ9PmAKICAgICAgICB9KQoKICAgICAgICByZXR1cm4gcHJvY2Vzc2VkQ29udGVudAogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g5L+d55WZ5a+M5paH5pys5qC85byP55So5LqO6aKE6KeI5pi+56S6CiAgICBwcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDkv53nlZnluLjnlKjnmoTlr4zmlofmnKzmoLzlvI/moIfnrb4KICAgICAgICBsZXQgcHJvY2Vzc2VkQ29udGVudCA9IGNvbnRlbnQKICAgICAgICAgIC8vIOi9rOaNouebuOWvuei3r+W+hOeahOWbvueJhwogICAgICAgICAgLnJlcGxhY2UoLzxpbWcoW14+XSo/KXNyYz0iKFteIl0qPykiKFtePl0qPyk+L2dpLCAobWF0Y2gsIGJlZm9yZSwgc3JjLCBhZnRlcikgPT4gewogICAgICAgICAgICBpZiAoIXNyYy5zdGFydHNXaXRoKCdodHRwJykgJiYgIXNyYy5zdGFydHNXaXRoKCdkYXRhOicpKSB7CiAgICAgICAgICAgICAgY29uc3QgZnVsbFNyYyA9IHRoaXMucHJvY2Vzc0ltYWdlUGF0aHMoc3JjKQogICAgICAgICAgICAgIHJldHVybiBgPGltZyR7YmVmb3JlfXNyYz0iJHtmdWxsU3JjfSIke2FmdGVyfT5gCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIG1hdGNoCiAgICAgICAgICB9KQogICAgICAgICAgLy8g5L+d55WZ5q616JC957uT5p6ECiAgICAgICAgICAucmVwbGFjZSgvPHBbXj5dKj4vZ2ksICc8cD4nKQogICAgICAgICAgLnJlcGxhY2UoLzxcL3A+L2dpLCAnPC9wPicpCiAgICAgICAgICAvLyDkv53nlZnmjaLooYwKICAgICAgICAgIC5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnPGJyPicpCiAgICAgICAgICAvLyDmuIXnkIblpJrkvZnnmoTnqbrnmb3mrrXokL0KICAgICAgICAgIC5yZXBsYWNlKC88cD5ccyo8XC9wPi9naSwgJycpCiAgICAgICAgICAucmVwbGFjZSgvKDxwPltcc1xuXSo8XC9wPikvZ2ksICcnKQoKICAgICAgICByZXR1cm4gcHJvY2Vzc2VkQ29udGVudC50cmltKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOenu+mZpEhUTUzmoIfnrb7kvYbkv53nlZnlm77niYfmoIfnrb4KICAgIHN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IGltYWdlcyA9IFtdCiAgICAgICAgbGV0IGltYWdlSW5kZXggPSAwCiAgICAgICAgY29uc3QgY29udGVudFdpdGhQbGFjZWhvbGRlcnMgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdbXj5dKj4vZ2ksIChtYXRjaCkgPT4gewogICAgICAgICAgaW1hZ2VzLnB1c2gobWF0Y2gpCiAgICAgICAgICByZXR1cm4gYFxuX19JTUFHRV9QTEFDRUhPTERFUl8ke2ltYWdlSW5kZXgrK31fX1xuYAogICAgICAgIH0pCgogICAgICAgIGxldCB0ZXh0Q29udGVudCA9IGNvbnRlbnRXaXRoUGxhY2Vob2xkZXJzCiAgICAgICAgICAucmVwbGFjZSgvPGJyXHMqXC8/Pi9naSwgJ1xuJykKICAgICAgICAgIC5yZXBsYWNlKC88XC9wPi9naSwgJ1xuJykKICAgICAgICAgIC5yZXBsYWNlKC88cFtePl0qPi9naSwgJ1xuJykKICAgICAgICAgIC5yZXBsYWNlKC88W14+XSo+L2csICcnKQogICAgICAgICAgLnJlcGxhY2UoL1xuXHMqXG4vZywgJ1xuJykKCiAgICAgICAgbGV0IGZpbmFsQ29udGVudCA9IHRleHRDb250ZW50CiAgICAgICAgaW1hZ2VzLmZvckVhY2goKGltZywgaW5kZXgpID0+IHsKICAgICAgICAgIGNvbnN0IHBsYWNlaG9sZGVyID0gYF9fSU1BR0VfUExBQ0VIT0xERVJfJHtpbmRleH1fX2AKICAgICAgICAgIGlmIChmaW5hbENvbnRlbnQuaW5jbHVkZXMocGxhY2Vob2xkZXIpKSB7CiAgICAgICAgICAgIGZpbmFsQ29udGVudCA9IGZpbmFsQ29udGVudC5yZXBsYWNlKHBsYWNlaG9sZGVyLCBpbWcpCiAgICAgICAgICB9CiAgICAgICAgfSkKCiAgICAgICAgcmV0dXJuIGZpbmFsQ29udGVudC50cmltKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOmAiemhuSAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VPcHRpb25zRnJvbUxpbmVzKGxpbmVzLCBzdGFydEluZGV4KSB7CiAgICAgIGNvbnN0IG9wdGlvbnMgPSBbXQoKICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGxpbmVzKSB8fCBzdGFydEluZGV4IDwgMCB8fCBzdGFydEluZGV4ID49IGxpbmVzLmxlbmd0aCkgewogICAgICAgIHJldHVybiB7IG9wdGlvbnMgfQogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGZvciAobGV0IGkgPSBzdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAgIGlmICghbGluZSB8fCB0eXBlb2YgbGluZSAhPT0gJ3N0cmluZycpIHsKICAgICAgICAgICAgY29udGludWUKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDkvb/nlKjkuKXmoLznmoTpgInpobnooYzpqozor4HpgLvovpEKICAgICAgICAgIGlmICh0aGlzLmlzT3B0aW9uTGluZShsaW5lKSkgewogICAgICAgICAgICBjb25zdCBvcHRpb25NYXRjaCA9IGxpbmUubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKQogICAgICAgICAgICBpZiAob3B0aW9uTWF0Y2gpIHsKICAgICAgICAgICAgICBjb25zdCBvcHRpb25LZXkgPSBvcHRpb25NYXRjaFsxXS50b1VwcGVyQ2FzZSgpCiAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uQ29udGVudCA9IG9wdGlvbk1hdGNoWzJdID8gb3B0aW9uTWF0Y2hbMl0udHJpbSgpIDogJycKCiAgICAgICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgICAgICBvcHRpb25zLnB1c2goewogICAgICAgICAgICAgICAgICBvcHRpb25LZXk6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgb3B0aW9uQ29udGVudDogb3B0aW9uQ29udGVudCwKICAgICAgICAgICAgICAgICAgY29udGVudDogb3B0aW9uQ29udGVudAogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5pc0Fuc3dlckxpbmUobGluZSkgfHwgdGhpcy5pc0V4cGxhbmF0aW9uTGluZShsaW5lKSB8fCB0aGlzLmlzRGlmZmljdWx0eUxpbmUobGluZSkpIHsKICAgICAgICAgICAgLy8g6YGH5Yiw562U5qGI44CB6Kej5p6Q5oiW6Zq+5bqm6KGM77yM5YGc5q2i6Kej5p6Q6YCJ6aG5CiAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDop4TojIPvvJrpgInpobnkuI7pgInpobnkuYvpl7TvvIzlj6/ku6XmjaLooYzvvIzkuZ/lj6/ku6XlnKjlkIzkuIDooYwKICAgICAgICAgICAgLy8g5aaC5p6c6YCJ6aG55Zyo5ZCM5LiA6KGM77yM6YCJ6aG55LmL6Ze06Iez5bCR6ZyA6KaB5pyJ5LiA5Liq56m65qC8CiAgICAgICAgICAgIC8vIOS9huaYr+imgemBv+WFjeivr+WwhumimOebruWGheWuueS4reeahOWtl+avjSvnrKblj7for4bliKvkuLrpgInpobkKICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T6KGM6ZW/5bqm6L6D55+t5LiU5LiN5YyF5ZCr5o+P6L+w5oCn5paH5a2X5pe25omN5bCd6K+V6Kej5p6Q5aSa6YCJ6aG5CiAgICAgICAgICAgIGlmIChsaW5lLmxlbmd0aCA8IDUwICYmICEv6KGo56S6fOaVsOaNrnzkuIDoiKx86YCa5bi4fOWPr+S7pS8udGVzdChsaW5lKSkgewogICAgICAgICAgICAgIGNvbnN0IG11bHRpcGxlT3B0aW9uc01hdGNoID0gbGluZS5tYXRjaCgvKFtBLVpdWy4677ya77yO44CBXVxzKlteXHNdKyg/OlxzK1tBLVpdWy4677ya77yO44CBXVxzKlteXHNdKykqKS9nKQogICAgICAgICAgICAgIGlmIChtdWx0aXBsZU9wdGlvbnNNYXRjaCkgewogICAgICAgICAgICAgICAgLy8g5aSE55CG5ZCM5LiA6KGM5aSa5Liq6YCJ6aG555qE5oOF5Ya1CiAgICAgICAgICAgICAgICBjb25zdCBzaW5nbGVPcHRpb25zID0gbGluZS5zcGxpdCgvXHMrKD89W0EtWmEtel1bLjrvvJrvvI7jgIFdKS8pCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHNpbmdsZU9wdGlvbiBvZiBzaW5nbGVPcHRpb25zKSB7CiAgICAgICAgICAgICAgICAgIGlmICghc2luZ2xlT3B0aW9uKSBjb250aW51ZQoKICAgICAgICAgICAgICAgICAgLy8g5L2/55So5Lil5qC855qE6YCJ6aG56aqM6K+B6YC76L6RCiAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmlzT3B0aW9uTGluZShzaW5nbGVPcHRpb24pKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSBzaW5nbGVPcHRpb24ubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKQogICAgICAgICAgICAgICAgICAgIGlmIChtYXRjaCkgewogICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gbWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uQ29udGVudCA9IG1hdGNoWzJdID8gbWF0Y2hbMl0udHJpbSgpIDogJycKCiAgICAgICAgICAgICAgICAgICAgICBpZiAob3B0aW9uS2V5ICYmIG9wdGlvbkNvbnRlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25LZXk6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogb3B0aW9uQ29udGVudAogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAvLyDlv73nlaXplJnor68KICAgICAgfQoKICAgICAgcmV0dXJuIHsgb3B0aW9ucyB9CiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOmimOebruWFg+S/oeaBryAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VRdWVzdGlvbk1ldGFGcm9tTGluZXMobGluZXMsIHF1ZXN0aW9uKSB7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICAgIGNvbnN0IGFuc3dlck1hdGNoID0gbGluZS5tYXRjaCgvXuetlOahiFsuOu+8muOAgV1ccyooLispLykKICAgICAgICBpZiAoYW5zd2VyTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLnBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyTWF0Y2hbMV0sIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrop6PmnpDmoLzlvI/vvIjop6PmnpDvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgICAgY29uc3QgZXhwbGFuYXRpb25NYXRjaCA9IGxpbmUubWF0Y2goL17op6PmnpBbLjrvvJrjgIFdXHMqKC4rKS8pCiAgICAgICAgaWYgKGV4cGxhbmF0aW9uTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmV4cGxhbmF0aW9uID0gZXhwbGFuYXRpb25NYXRjaFsxXS50cmltKCkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrpmr7luqbmoLzlvI/vvIjpmr7luqbvvJrvvInvvIzlj6rmlK/mjIHnroDljZXjgIHkuK3nrYnjgIHlm7Dpmr7kuInkuKrnuqfliKsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWF0Y2ggPSBsaW5lLm1hdGNoKC9e6Zq+5bqmWy4677ya44CBXVxzKijnroDljZV85Lit562JfOWbsOmavnzkuK0pLykKICAgICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgICBsZXQgZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXRjaFsxXQogICAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgICAgaWYgKGRpZmZpY3VsdHkgPT09ICfkuK0nKSB7CiAgICAgICAgICAgIGRpZmZpY3VsdHkgPSAn5Lit562JJwogICAgICAgICAgfQogICAgICAgICAgLy8g5Y+q5o6l5Y+X5qCH5YeG55qE5LiJ5Liq6Zq+5bqm57qn5YirCiAgICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uLmRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5CiAgICAgICAgICB9CiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6KeE6IyD77ya562U5qGI5pSv5oyB55u05o6l5Zyo6aKY5bmy5Lit5qCH5rOo77yM5LyY5YWI5Lul5pi+5byP5qCH5rOo55qE562U5qGI5Li65YeGCiAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOaYvuW8j+etlOahiO+8jOWwneivleS7jumimOebruWGheWuueS4reaPkOWPlgogICAgICBpZiAoIXF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIpIHsKICAgICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5leHRyYWN0QW5zd2VyRnJvbVF1ZXN0aW9uQ29udGVudChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDku47popjlubLkuK3mj5Dlj5bnrZTmoYggLSDmjInnhafovpPlhaXop4TojIMKICAgIGV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghcXVlc3Rpb25Db250ZW50IHx8IHR5cGVvZiBxdWVzdGlvbkNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g6KeE6IyD77ya6aKY5bmy5Lit5qC85byP77yI44CQQeOAke+8ie+8jOaLrOWPt+WPr+S7peabv+aNouS4uuS4reiLseaWh+eahOWwj+aLrOWPt+aIluiAheS4reaLrOWPtwogICAgICAgIGNvbnN0IHBhdHRlcm5zID0gWwogICAgICAgICAgL+OAkChbXuOAkV0rKeOAkS9nLCAgICAvLyDkuK3mlofmlrnmi6zlj7cKICAgICAgICAgIC9cWyhbXlxdXSspXF0vZywgICAvLyDoi7Hmlofmlrnmi6zlj7cKICAgICAgICAgIC/vvIgoW17vvIldKynvvIkvZywgICAgLy8g5Lit5paH5ZyG5ous5Y+3CiAgICAgICAgICAvXCgoW14pXSspXCkvZyAgICAgLy8g6Iux5paH5ZyG5ous5Y+3CiAgICAgICAgXQoKICAgICAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgcGF0dGVybnMpIHsKICAgICAgICAgIGNvbnN0IG1hdGNoZXMgPSBxdWVzdGlvbkNvbnRlbnQubWF0Y2gocGF0dGVybikKICAgICAgICAgIGlmIChtYXRjaGVzICYmIG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAvLyDmj5Dlj5bmnIDlkI7kuIDkuKrljLnphY3pobnkvZzkuLrnrZTmoYjvvIjpgJrluLjnrZTmoYjlnKjpopjnm67mnKvlsL7vvIkKICAgICAgICAgICAgY29uc3QgbGFzdE1hdGNoID0gbWF0Y2hlc1ttYXRjaGVzLmxlbmd0aCAtIDFdCiAgICAgICAgICAgIGNvbnN0IGFuc3dlciA9IGxhc3RNYXRjaC5yZXBsYWNlKC9b44CQ44CRXFtcXe+8iO+8iSgpXS9nLCAnJykudHJpbSgpCgogICAgICAgICAgICBpZiAoYW5zd2VyKSB7CiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXIsIHF1ZXN0aW9uVHlwZSkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgIH0KCiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvLyDop6PmnpDnrZTmoYjlgLwKICAgIHBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyVGV4dCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghYW5zd2VyVGV4dCB8fCB0eXBlb2YgYW5zd2VyVGV4dCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCB0cmltbWVkQW5zd2VyID0gYW5zd2VyVGV4dC50cmltKCkKCiAgICAgICAgaWYgKCF0cmltbWVkQW5zd2VyKSB7CiAgICAgICAgICByZXR1cm4gJycKICAgICAgICB9CgogICAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcpIHsKICAgICAgICAgIC8vIOWIpOaWremimOetlOahiOWkhOeQhiAtIOS/neaMgeWOn+Wni+agvOW8j++8jOS4jei9rOaNouS4unRydWUvZmFsc2UKICAgICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOmAieaLqemimOetlOahiOWkhOeQhgogICAgICAgICAgcmV0dXJuIHRyaW1tZWRBbnN3ZXIudG9VcHBlckNhc2UoKQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHJldHVybiBhbnN3ZXJUZXh0IHx8ICcnCiAgICAgICAgfQogICAgfSwKCgoKCgogICAgLy8g6I635Y+W5qC85byP5YyW55qE6aKY55uu5YaF5a6577yI5pSv5oyB5a+M5paH5pys5qC85byP77yJCiAgICBnZXRGb3JtYXR0ZWRRdWVzdGlvbkNvbnRlbnQocXVlc3Rpb24pIHsKICAgICAgaWYgKCFxdWVzdGlvbiB8fCAhcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIGxldCBjb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50CgogICAgICAvLyDlpoLmnpzmnIlIVE1M5YaF5a655LiU5YyF5ZCr5a+M5paH5pys5qCH562+77yM5LyY5YWI5L2/55SoSFRNTOWGheWuuQogICAgICBpZiAodGhpcy5kb2N1bWVudEh0bWxDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgICAgIGNvbnN0IGh0bWxDb250ZW50ID0gdGhpcy5leHRyYWN0UXVlc3Rpb25Gcm9tSHRtbChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCkKICAgICAgICBpZiAoaHRtbENvbnRlbnQpIHsKICAgICAgICAgIGNvbnRlbnQgPSBodG1sQ29udGVudAogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5riF55CG6aKY5Y+377ya56Gu5L+d6aKY55uu5YaF5a655LiN5Lul5pWw5a2XK+espuWPt+W8gOWktAogICAgICBjb250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihjb250ZW50KQoKICAgICAgLy8g5riF55CG6aKY5Z6L5qCH6K+G77ya56e76Zmk6aKY55uu5YaF5a655byA5aS055qEW+mimOWei13moIfor4YKICAgICAgY29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25UeXBlKGNvbnRlbnQpCgogICAgICByZXR1cm4gdGhpcy5wcm9jZXNzSW1hZ2VQYXRocyhjb250ZW50KQogICAgfSwKCiAgICAvLyDojrflj5bpopjlnovlkI3np7AKICAgIGdldFF1ZXN0aW9uVHlwZU5hbWUodHlwZSkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICdzaW5nbGUnOiAn5Y2V6YCJ6aKYJywKICAgICAgICAnbXVsdGlwbGUnOiAn5aSa6YCJ6aKYJywKICAgICAgICAnanVkZ21lbnQnOiAn5Yik5pat6aKYJwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6UnCiAgICB9LAoKICAgIC8vIOa4heeQhumimOebruWGheWuueS4reeahOmimOWPtwogICAgcmVtb3ZlUXVlc3Rpb25OdW1iZXIoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuIGNvbnRlbnQKICAgICAgfQoKICAgICAgLy8g5aSE55CGSFRNTOWGheWuuQogICAgICBpZiAoY29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgLy8g5a+55LqOSFRNTOWGheWuue+8jOmcgOimgea4heeQhuagh+etvuWGheeahOmimOWPtwogICAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoLzxwW14+XSo+KFxzKlxkK1suOu+8mu+8juOAgV1ccyopKC4qPyk8XC9wPi9naSwgJzxwPiQyPC9wPicpCiAgICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9eKFxzKlxkK1suOu+8mu+8juOAgV1ccyopLywgJycpIC8vIOa4heeQhuW8gOWktOeahOmimOWPtwogICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvPlxzKlxkK1suOu+8mu+8juOAgV1ccyovZywgJz4nKSAvLyDmuIXnkIbmoIfnrb7lkI7nmoTpopjlj7cKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlr7nkuo7nuq/mlofmnKzlhoXlrrnvvIznm7TmjqXmuIXnkIblvIDlpLTnmoTpopjlj7cKICAgICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgfQogICAgfSwKCiAgICAvLyDmuIXnkIbpopjnm67lhoXlrrnkuK3nmoTpopjlnovmoIfor4YKICAgIHJlbW92ZVF1ZXN0aW9uVHlwZShjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CgogICAgICAvLyDlpITnkIZIVE1M5YaF5a65CiAgICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICAvLyDlr7nkuo5IVE1M5YaF5a6577yM5riF55CG5qCH562+5YaF55qE6aKY5Z6L5qCH6K+GCiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvPHBbXj5dKj4oXHMqXFsuKj/pophcXVxzKikoLio/KTxcL3A+L2dpLCAnPHA+JDI8L3A+JykKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL14oXHMqXFsuKj/pophcXVxzKikvLCAnJykgLy8g5riF55CG5byA5aS055qE6aKY5Z6L5qCH6K+GCiAgICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC8+XHMqXFsuKj/pophcXVxzKi9nLCAnPicpIC8vIOa4heeQhuagh+etvuWQjueahOmimOWei+agh+ivhgogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWvueS6jue6r+aWh+acrOWGheWuue+8jOa4heeQhuW8gOWktOeahOmimOWei+agh+ivhgogICAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoL15ccypcWy4qP+mimFxdXHMqLywgJycpLnRyaW0oKQogICAgICB9CiAgICB9LAoKICAgIC8vIOS7jkhUTUzlhoXlrrnkuK3mj5Dlj5blr7nlupTnmoTpopjnm67lhoXlrrkKICAgIGV4dHJhY3RRdWVzdGlvbkZyb21IdG1sKHBsYWluQ29udGVudCwgaHRtbENvbnRlbnQpIHsKICAgICAgaWYgKCFwbGFpbkNvbnRlbnQgfHwgIWh0bWxDb250ZW50KSB7CiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOeugOWNleeahOWMuemFjeetlueVpe+8muafpeaJvuWMheWQq+mimOebruWGheWuueeahEhUTUzmrrXokL0KICAgICAgICBjb25zdCBwbGFpblRleHQgPSBwbGFpbkNvbnRlbnQucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCgogICAgICAgIC8vIOWcqEhUTUzlhoXlrrnkuK3mn6Xmib7ljIXlkKvov5nkuKrmlofmnKznmoTmrrXokL0KICAgICAgICBjb25zdCBwYXJhZ3JhcGhzID0gaHRtbENvbnRlbnQubWF0Y2goLzxwW14+XSo+Lio/PFwvcD4vZ2kpIHx8IFtdCgogICAgICAgIGZvciAoY29uc3QgcGFyYWdyYXBoIG9mIHBhcmFncmFwaHMpIHsKICAgICAgICAgIGNvbnN0IHBhcmFncmFwaFRleHQgPSBwYXJhZ3JhcGgucmVwbGFjZSgvPFtePl0qPi9nLCAnJykudHJpbSgpCiAgICAgICAgICAvLyDmuIXnkIbmrrXokL3mlofmnKzkuK3nmoTpopjlj7flho3ov5vooYzljLnphY0KICAgICAgICAgIGNvbnN0IGNsZWFuUGFyYWdyYXBoVGV4dCA9IHBhcmFncmFwaFRleHQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgICAgICBpZiAoY2xlYW5QYXJhZ3JhcGhUZXh0LmluY2x1ZGVzKHBsYWluVGV4dC5zdWJzdHJpbmcoMCwgMjApKSkgewogICAgICAgICAgICAvLyDmib7liLDljLnphY3nmoTmrrXokL3vvIzov5Tlm55IVE1M5qC85byP77yI5L2G6KaB5riF55CG6aKY5Y+377yJCiAgICAgICAgICAgIHJldHVybiB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHBhcmFncmFwaCkKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIHJldHVybiBwbGFpbkNvbnRlbnQKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICByZXR1cm4gcGxhaW5Db250ZW50CiAgICAgIH0KICAgIH0sCgoKICAgIC8vIOaQnOe0ogogICAgaGFuZGxlU2VhcmNoKCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgIH0sCiAgICAvLyDph43nva7mkJzntKIKICAgIHJlc2V0U2VhcmNoKCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnF1ZXN0aW9uVHlwZSA9IG51bGwKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaWZmaWN1bHR5ID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnF1ZXN0aW9uQ29udGVudCA9IG51bGwKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQogICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"]}]}